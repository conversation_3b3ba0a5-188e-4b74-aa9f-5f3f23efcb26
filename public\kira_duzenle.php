<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$daireler = $db->query('SELECT a.id, a.number, b.name as block_name FROM apartments a LEFT JOIN blocks b ON a.block_id = b.id ORDER BY b.name, a.number')->fetchAll();

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: kiralar.php');
    exit;
}
$id = (int)$_GET['id'];
$kira = $db->prepare('SELECT * FROM payments WHERE id = ?');
$kira->execute([$id]);
$kira = $kira->fetch();
if (!$kira) {
    header('Location: kiralar.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $apartment_id = $_POST['apartment_id'] ?? '';
    $amount = trim($_POST['amount'] ?? '');
    $payment_date = trim($_POST['payment_date'] ?? '');
    $payment_method = trim($_POST['payment_method'] ?? '');
    $description = trim($_POST['description'] ?? '');
    if (!$apartment_id || !$amount || !$payment_date || !$payment_method) {
        $error = 'Daire, tutar, ödeme tarihi ve ödeme yöntemi zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE payments SET apartment_id=?, amount=?, payment_date=?, payment_method=?, description=? WHERE id=?');
        $stmt->execute([$apartment_id, $amount, $payment_date, $payment_method, $description, $id]);
        header('Location: kiralar.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Kira Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Kira Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Daire</label>
            <select name="apartment_id" class="form-select" required>
                <option value="">Seçiniz</option>
                <?php foreach ($daireler as $daire): ?>
                    <option value="<?php echo $daire['id']; ?>" <?php if($kira['apartment_id']==$daire['id']) echo 'selected'; ?>><?php echo htmlspecialchars($daire['block_name'] . ' - ' . $daire['number']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Tutar (TL)</label>
            <input type="number" step="0.01" name="amount" class="form-control" value="<?php echo htmlspecialchars($kira['amount']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Ödeme Tarihi</label>
            <input type="date" name="payment_date" class="form-control" value="<?php echo htmlspecialchars($kira['payment_date']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Ödeme Yöntemi</label>
            <input type="text" name="payment_method" class="form-control" value="<?php echo htmlspecialchars($kira['payment_method']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="description" class="form-control" rows="2"><?php echo htmlspecialchars($kira['description']); ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="kiralar.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 