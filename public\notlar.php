<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();

$db = getDBConnection();

// Notları getir
$notlar = $db->prepare('SELECT * FROM notes WHERE user_id = ? ORDER BY created_at DESC');
$notlar->execute([$user['id']]);
$notlar = $notlar->fetchAll();

// Not silme
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    // Sadece kendi notlarını silebilir
    $stmt = $db->prepare('DELETE FROM notes WHERE id = ? AND user_id = ?');
    $stmt->execute([$id, $user['id']]);
    header('Location: notlar.php');
    exit;
}

// Not ekleme
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_note'])) {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $color = $_POST['color'] ?? '#ffffff';
    
    if (!$title || !$content) {
        $error = 'Başlık ve içerik zorunludur!';
    } else {
        $stmt = $db->prepare('INSERT INTO notes (user_id, title, content, color, created_at) VALUES (?, ?, ?, ?, NOW())');
        $stmt->execute([$user['id'], $title, $content, $color]);
        $success = 'Not başarıyla eklendi!';
        // Sayfayı yenile
        header('Location: notlar.php');
        exit;
    }
}

// Not düzenleme
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_note'])) {
    $id = (int)$_POST['note_id'];
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $color = $_POST['color'] ?? '#ffffff';
    
    if (!$title || !$content) {
        $error = 'Başlık ve içerik zorunludur!';
    } else {
        // Sadece kendi notlarını düzenleyebilir
        $stmt = $db->prepare('UPDATE notes SET title = ?, content = ?, color = ? WHERE id = ? AND user_id = ?');
        $stmt->execute([$title, $content, $color, $id, $user['id']]);
        $success = 'Not başarıyla güncellendi!';
        // Sayfayı yenile
        header('Location: notlar.php');
        exit;
    }
}

// Renk seçenekleri
$colors = [
    '#ffffff' => 'Beyaz',
    '#f8d7da' => 'Kırmızı',
    '#d1e7dd' => 'Yeşil',
    '#cfe2ff' => 'Mavi',
    '#fff3cd' => 'Sarı',
    '#e2e3e5' => 'Gri'
];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Notlarım - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .note-card {
            transition: transform 0.2s;
            height: 100%;
        }
        .note-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .note-content {
            white-space: pre-line;
            max-height: 150px;
            overflow-y: auto;
        }
        .color-option {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            cursor: pointer;
            border: 2px solid #dee2e6;
        }
        .color-option.active {
            border: 2px solid #000;
        }
    </style>
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Notlarım</h2>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addNoteModal">
            <i class="bi bi-plus-lg"></i> Yeni Not Ekle
        </button>
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <!-- Not Listesi -->
    <div class="row g-4">
        <?php if (empty($notlar)): ?>
            <div class="col-12">
                <div class="alert alert-info">Henüz not eklenmemiş.</div>
            </div>
        <?php else: ?>
            <?php foreach ($notlar as $not): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="card note-card" style="background-color: <?php echo htmlspecialchars($not['color']); ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title"><?php echo htmlspecialchars($not['title']); ?></h5>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-link text-dark" type="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#editNoteModal<?php echo $not['id']; ?>">
                                                <i class="bi bi-pencil"></i> Düzenle
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="?sil=<?php echo $not['id']; ?>" onclick="return confirm('Bu notu silmek istediğinize emin misiniz?');">
                                                <i class="bi bi-trash"></i> Sil
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="note-content"><?php echo nl2br(htmlspecialchars($not['content'])); ?></div>
                            <div class="text-muted mt-3">
                                <small><?php echo date('d.m.Y H:i', strtotime($not['created_at'])); ?></small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Not Düzenleme Modal -->
                    <div class="modal fade" id="editNoteModal<?php echo $not['id']; ?>" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Not Düzenle</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form method="post">
                                        <input type="hidden" name="note_id" value="<?php echo $not['id']; ?>">
                                        <div class="mb-3">
                                            <label class="form-label">Başlık</label>
                                            <input type="text" name="title" class="form-control" value="<?php echo htmlspecialchars($not['title']); ?>" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">İçerik</label>
                                            <textarea name="content" class="form-control" rows="5" required><?php echo htmlspecialchars($not['content']); ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Renk</label>
                                            <div>
                                                <?php foreach ($colors as $value => $name): ?>
                                                    <label class="color-option-container">
                                                        <input type="radio" name="color" value="<?php echo $value; ?>" <?php if ($not['color'] === $value) echo 'checked'; ?> class="d-none">
                                                        <span class="color-option" style="background-color: <?php echo $value; ?>" title="<?php echo $name; ?>"></span>
                                                    </label>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                                            <button type="submit" name="edit_note" class="btn btn-primary">Güncelle</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <a href="index.php" class="btn btn-secondary mt-4">
        <i class="bi bi-arrow-left"></i> Ana Sayfa
    </a>
</div>

<!-- Yeni Not Ekleme Modal -->
<div class="modal fade" id="addNoteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Yeni Not Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">Başlık</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">İçerik</label>
                        <textarea name="content" class="form-control" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Renk</label>
                        <div>
                            <?php foreach ($colors as $value => $name): ?>
                                <label class="color-option-container">
                                    <input type="radio" name="color" value="<?php echo $value; ?>" <?php if ($value === '#ffffff') echo 'checked'; ?> class="d-none">
                                    <span class="color-option" style="background-color: <?php echo $value; ?>" title="<?php echo $name; ?>"></span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                        <button type="submit" name="add_note" class="btn btn-success">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Renk seçimi
        const colorOptions = document.querySelectorAll('.color-option');
        
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                const container = this.closest('.color-option-container');
                const radio = container.querySelector('input[type="radio"]');
                radio.checked = true;
            });
        });
    });
</script>
</body>
</html>
