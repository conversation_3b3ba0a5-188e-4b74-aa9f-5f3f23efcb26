<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$kiralar = $db->query('SELECT k.*, a.number as daire_no, b.name as blok_adi FROM payments k LEFT JOIN apartments a ON k.apartment_id = a.id LEFT JOIN blocks b ON a.block_id = b.id ORDER BY k.id DESC')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM payments WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: kiralar.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON> - Apartman <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Kiralar</h2>
        <a href="kira_ekle.php" class="btn btn-success"><i class="bi bi-plus"></i> Yeni Kira</a>
    </div>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
        <tr>
            <th>#</th>
            <th>Blok</th>
            <th>Daire No</th>
            <th>Tutar</th>
            <th>Ödeme Tarihi</th>
            <th>Ödeme Yöntemi</th>
            <th>Açıklama</th>
            <th>İşlemler</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($kiralar as $kira): ?>
            <tr>
                <td><?php echo $kira['id']; ?></td>
                <td><?php echo htmlspecialchars($kira['blok_adi']); ?></td>
                <td><?php echo htmlspecialchars($kira['daire_no']); ?></td>
                <td><?php echo number_format($kira['amount'], 2); ?> TL</td>
                <td><?php echo date('d.m.Y', strtotime($kira['payment_date'])); ?></td>
                <td><?php echo htmlspecialchars($kira['payment_method']); ?></td>
                <td><?php echo htmlspecialchars($kira['description']); ?></td>
                <td>
                    <a href="kira_duzenle.php?id=<?php echo $kira['id']; ?>" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                    <a href="?sil=<?php echo $kira['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Kira kaydı silinsin mi?');"><i class="bi bi-trash"></i></a>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-secondary mt-3"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
</div>
</body>
</html> 