<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();
$daireler = $db->query('SELECT a.id, a.number, b.name as block_name FROM apartments a LEFT JOIN blocks b ON a.block_id = b.id ORDER BY b.name, a.number')->fetchAll();

$apartment_id = $_GET['apartment_id'] ?? '';
$date1 = $_GET['date1'] ?? '';
$date2 = $_GET['date2'] ?? '';

$gelirler = $borclar = $giderler = [];
$total_income = $total_debt = $total_expense = 0;
if ($apartment_id) {
    // Gelirler (aidat/kira ödemeleri)
    $query = "SELECT 'Aidat' as type, amount, payment_date as date, 'Aidat Ödemesi' as description FROM dues WHERE apartment_id=? AND status='paid'";
    $params = [$apartment_id];
    if ($date1) { $query .= " AND payment_date >= ?"; $params[] = $date1; }
    if ($date2) { $query .= " AND payment_date <= ?"; $params[] = $date2; }
    $query .= " UNION ALL SELECT 'Kira' as type, amount, payment_date as date, description FROM payments WHERE apartment_id=?";
    $params2 = [$apartment_id];
    if ($date1) { $query .= " AND payment_date >= ?"; $params2[] = $date1; }
    if ($date2) { $query .= " AND payment_date <= ?"; $params2[] = $date2; }
    $params = array_merge($params, $params2);
    $gelirler = $db->prepare($query);
    $gelirler->execute($params);
    $gelirler = $gelirler->fetchAll();
    foreach ($gelirler as $g) $total_income += (float)$g['amount'];

    // Borçlar (ödenmemiş aidat/borçlar)
    $query = "SELECT amount, due_date as date, status, description FROM dues WHERE apartment_id=? AND status!='paid'";
    $params = [$apartment_id];
    if ($date1) { $query .= " AND due_date >= ?"; $params[] = $date1; }
    if ($date2) { $query .= " AND due_date <= ?"; $params[] = $date2; }
    $borclar = $db->prepare($query);
    $borclar->execute($params);
    $borclar = $borclar->fetchAll();
    foreach ($borclar as $b) $total_debt += (float)$b['amount'];

    // Giderler (daireye özel giderler varsa, örnek için boş bırakıldı)
    // $giderler = ...
    // $total_expense = ...
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Daire Ekstresi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Daire Ekstresi</h2>
    <form class="row g-3 mb-4" method="get">
        <div class="col-md-4">
            <label class="form-label">Daire</label>
            <select name="apartment_id" class="form-select" required>
                <option value="">Seçiniz</option>
                <?php foreach ($daireler as $d): ?>
                    <option value="<?php echo $d['id']; ?>" <?php if($apartment_id==$d['id']) echo 'selected'; ?>><?php echo htmlspecialchars($d['block_name'].'-'.$d['number']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">Başlangıç Tarihi</label>
            <input type="date" name="date1" class="form-control" value="<?php echo htmlspecialchars($date1); ?>">
        </div>
        <div class="col-md-3">
            <label class="form-label">Bitiş Tarihi</label>
            <input type="date" name="date2" class="form-control" value="<?php echo htmlspecialchars($date2); ?>">
        </div>
        <div class="col-md-2 align-self-end">
            <button type="submit" class="btn btn-primary w-100">Getir</button>
        </div>
    </form>
    <?php if ($apartment_id): ?>
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-bg-success mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Gelir</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_income,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-bg-danger mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Borç</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_debt,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
    </div>
    <h4>Gelirler (Ödenenler)</h4>
    <table class="table table-bordered table-hover align-middle mb-4">
        <thead class="table-light">
            <tr><th>Tarih</th><th>Tutar</th><th>Tür</th><th>Açıklama</th></tr>
        </thead>
        <tbody>
        <?php foreach ($gelirler as $g): ?>
            <tr>
                <td><?php echo htmlspecialchars($g['date']); ?></td>
                <td><?php echo number_format($g['amount'],2,',','.'); ?> ₺</td>
                <td><?php echo htmlspecialchars($g['type']); ?></td>
                <td><?php echo htmlspecialchars($g['description']); ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <h4>Borçlar (Ödenmemiş)</h4>
    <table class="table table-bordered table-hover align-middle mb-4">
        <thead class="table-light">
            <tr><th>Tarih</th><th>Tutar</th><th>Durum</th><th>Açıklama</th></tr>
        </thead>
        <tbody>
        <?php foreach ($borclar as $b): ?>
            <tr>
                <td><?php echo htmlspecialchars($b['date']); ?></td>
                <td><?php echo number_format($b['amount'],2,',','.'); ?> ₺</td>
                <td>
                    <?php if($b['status']==='overdue'): ?>
                        <span class="badge bg-danger">Gecikmiş</span>
                    <?php else: ?>
                        <span class="badge bg-warning text-dark">Ödenmedi</span>
                    <?php endif; ?>
                </td>
                <td><?php echo htmlspecialchars($b['description']); ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <?php endif; ?>
</div>
</body>
</html> 