<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();
$blocks = $db->query('SELECT * FROM blocks ORDER BY name')->fetchAll();
$daireler = $db->query('SELECT a.*, b.name as block_name FROM apartments a LEFT JOIN blocks b ON a.block_id = b.id ORDER BY b.name, a.number')->fetchAll();

// Filtreler
$block_id = $_GET['block_id'] ?? '';
$apartment_id = $_GET['apartment_id'] ?? '';
$status = $_GET['status'] ?? '';
$date1 = $_GET['date1'] ?? '';
$date2 = $_GET['date2'] ?? '';

$query = "SELECT d.*, a.number as daire_no, b.name as blok_adi FROM dues d LEFT JOIN apartments a ON d.apartment_id = a.id LEFT JOIN blocks b ON a.block_id = b.id WHERE 1=1";
$params = [];
if ($block_id) {
    $query .= " AND b.id = ?";
    $params[] = $block_id;
}
if ($apartment_id) {
    $query .= " AND a.id = ?";
    $params[] = $apartment_id;
}
if ($status) {
    $query .= " AND d.status = ?";
    $params[] = $status;
}
if ($date1) {
    $query .= " AND d.due_date >= ?";
    $params[] = $date1;
}
if ($date2) {
    $query .= " AND d.due_date <= ?";
    $params[] = $date2;
}
$query .= " ORDER BY d.due_date DESC";
$aidatlar = $db->prepare($query);
$aidatlar->execute($params);
$aidatlar = $aidatlar->fetchAll();
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Aidat Ödeme Durumu Raporu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Aidat Ödeme Durumu Raporu</h2>
    <form class="row g-3 mb-4" method="get">
        <div class="col-md-2">
            <label class="form-label">Blok</label>
            <select name="block_id" class="form-select">
                <option value="">Tümü</option>
                <?php foreach ($blocks as $b): ?>
                    <option value="<?php echo $b['id']; ?>" <?php if($block_id==$b['id']) echo 'selected'; ?>><?php echo htmlspecialchars($b['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Daire</label>
            <select name="apartment_id" class="form-select">
                <option value="">Tümü</option>
                <?php foreach ($daireler as $d): ?>
                    <option value="<?php echo $d['id']; ?>" <?php if($apartment_id==$d['id']) echo 'selected'; ?>><?php echo htmlspecialchars($d['block_name'].'-'.$d['number']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Durum</label>
            <select name="status" class="form-select">
                <option value="">Tümü</option>
                <option value="unpaid" <?php if($status==='unpaid') echo 'selected'; ?>>Ödenmedi</option>
                <option value="paid" <?php if($status==='paid') echo 'selected'; ?>>Ödendi</option>
                <option value="overdue" <?php if($status==='overdue') echo 'selected'; ?>>Gecikmiş</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Başlangıç Tarihi</label>
            <input type="date" name="date1" class="form-control" value="<?php echo htmlspecialchars($date1); ?>">
        </div>
        <div class="col-md-2">
            <label class="form-label">Bitiş Tarihi</label>
            <input type="date" name="date2" class="form-control" value="<?php echo htmlspecialchars($date2); ?>">
        </div>
        <div class="col-md-2 align-self-end">
            <button type="submit" class="btn btn-primary w-100">Filtrele</button>
        </div>
    </form>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
            <tr>
                <th>Blok</th>
                <th>Daire</th>
                <th>Tutar</th>
                <th>Son Ödeme Tarihi</th>
                <th>Durum</th>
                <th>Ödeme Tarihi</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($aidatlar as $a): ?>
            <tr>
                <td><?php echo htmlspecialchars($a['blok_adi']); ?></td>
                <td><?php echo htmlspecialchars($a['daire_no']); ?></td>
                <td><?php echo number_format($a['amount'],2,',','.'); ?> ₺</td>
                <td><?php echo htmlspecialchars($a['due_date']); ?></td>
                <td>
                    <?php if($a['status']==='paid'): ?>
                        <span class="badge bg-success">Ödendi</span>
                    <?php elseif($a['status']==='overdue'): ?>
                        <span class="badge bg-danger">Gecikmiş</span>
                    <?php else: ?>
                        <span class="badge bg-warning text-dark">Ödenmedi</span>
                    <?php endif; ?>
                </td>
                <td><?php echo htmlspecialchars($a['payment_date']); ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>
</body>
</html> 