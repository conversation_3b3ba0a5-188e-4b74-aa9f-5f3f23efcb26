<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();

// Filtreler
$category = $_GET['category'] ?? '';
$date1 = $_GET['date1'] ?? '';
$date2 = $_GET['date2'] ?? '';
$desc = $_GET['desc'] ?? '';

$query = "SELECT * FROM expenses WHERE 1=1";
$params = [];
if ($category) {
    $query .= " AND category ILIKE ?";
    $params[] = "%$category%";
}
if ($date1) {
    $query .= " AND date >= ?";
    $params[] = $date1;
}
if ($date2) {
    $query .= " AND date <= ?";
    $params[] = $date2;
}
if ($desc) {
    $query .= " AND description ILIKE ?";
    $params[] = "%$desc%";
}
$query .= " ORDER BY date DESC";
$giderler = $db->prepare($query);
$giderler->execute($params);
$giderler = $giderler->fetchAll();

$total = 0;
foreach ($giderler as $g) $total += (float)$g['amount'];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Gider Raporu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Gider Raporu</h2>
    <form class="row g-3 mb-4" method="get">
        <div class="col-md-3">
            <label class="form-label">Kategori</label>
            <input type="text" name="category" class="form-control" value="<?php echo htmlspecialchars($category); ?>">
        </div>
        <div class="col-md-2">
            <label class="form-label">Başlangıç Tarihi</label>
            <input type="date" name="date1" class="form-control" value="<?php echo htmlspecialchars($date1); ?>">
        </div>
        <div class="col-md-2">
            <label class="form-label">Bitiş Tarihi</label>
            <input type="date" name="date2" class="form-control" value="<?php echo htmlspecialchars($date2); ?>">
        </div>
        <div class="col-md-3">
            <label class="form-label">Açıklama</label>
            <input type="text" name="desc" class="form-control" value="<?php echo htmlspecialchars($desc); ?>">
        </div>
        <div class="col-md-2 align-self-end">
            <button type="submit" class="btn btn-primary w-100">Filtrele</button>
        </div>
    </form>
    <div class="mb-3">
        <span class="fw-bold">Toplam Gider:</span> <?php echo number_format($total,2,',','.'); ?> ₺
    </div>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
            <tr>
                <th>Tarih</th>
                <th>Kategori</th>
                <th>Tutar</th>
                <th>Açıklama</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($giderler as $g): ?>
            <tr>
                <td><?php echo htmlspecialchars($g['date']); ?></td>
                <td><?php echo htmlspecialchars($g['category']); ?></td>
                <td><?php echo number_format($g['amount'],2,',','.'); ?> ₺</td>
                <td><?php echo htmlspecialchars($g['description']); ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>
</body>
</html> 