<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();

// Firma listesini al
$firmalar = $db->query('SELECT * FROM services ORDER BY name')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM services WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: hizmetler.php');
    exit;
}

// Yeni firma ekleme
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_service'])) {
    $name = trim($_POST['name'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $contact_person = trim($_POST['contact_person'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (!$name || !$category || !$phone) {
        $error = 'Firma adı, kategori ve telefon zorunludur!';
    } else {
        $stmt = $db->prepare('INSERT INTO services (name, category, contact_person, phone, email, address, description, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())');
        $stmt->execute([$name, $category, $contact_person, $phone, $email, $address, $description]);
        $success = 'Firma başarıyla eklendi!';
        // Sayfayı yenile
        header('Location: hizmetler.php');
        exit;
    }
}

// Kategori listesi
$categories = [
    'Elektrik',
    'Su Tesisatı',
    'İnşaat',
    'Temizlik',
    'Güvenlik',
    'Asansör',
    'Bahçe Bakımı',
    'Isıtma-Soğutma',
    'Diğer'
];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Hizmetler ve Firmalar - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .service-card {
            transition: transform 0.2s;
            height: 100%;
        }
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .category-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Hizmetler ve Firmalar</h2>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addServiceModal">
            <i class="bi bi-plus-lg"></i> Yeni Firma Ekle
        </button>
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <!-- Kategori Filtreleme -->
    <div class="mb-4">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary active filter-btn" data-filter="all">Tümü</button>
            <?php foreach ($categories as $category): ?>
                <button type="button" class="btn btn-outline-primary filter-btn" data-filter="<?php echo htmlspecialchars($category); ?>">
                    <?php echo htmlspecialchars($category); ?>
                </button>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- Firma Listesi -->
    <div class="row g-4" id="services-container">
        <?php if (empty($firmalar)): ?>
            <div class="col-12">
                <div class="alert alert-info">Henüz kayıtlı firma bulunmuyor.</div>
            </div>
        <?php else: ?>
            <?php foreach ($firmalar as $firma): ?>
                <div class="col-md-6 col-lg-4 service-item" data-category="<?php echo htmlspecialchars($firma['category']); ?>">
                    <div class="card service-card">
                        <div class="card-body">
                            <span class="badge bg-primary category-badge"><?php echo htmlspecialchars($firma['category']); ?></span>
                            <h5 class="card-title"><?php echo htmlspecialchars($firma['name']); ?></h5>
                            <p class="card-text text-muted mb-1">
                                <i class="bi bi-person"></i> <?php echo htmlspecialchars($firma['contact_person']); ?>
                            </p>
                            <p class="card-text mb-1">
                                <i class="bi bi-telephone"></i> <?php echo htmlspecialchars($firma['phone']); ?>
                            </p>
                            <?php if ($firma['email']): ?>
                                <p class="card-text mb-1">
                                    <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($firma['email']); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($firma['address']): ?>
                                <p class="card-text mb-1">
                                    <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($firma['address']); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($firma['description']): ?>
                                <p class="card-text mt-2">
                                    <small><?php echo nl2br(htmlspecialchars($firma['description'])); ?></small>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent d-flex justify-content-between">
                            <small class="text-muted">Eklenme: <?php echo date('d.m.Y', strtotime($firma['created_at'])); ?></small>
                            <div>
                                <a href="hizmet_duzenle.php?id=<?php echo $firma['id']; ?>" class="btn btn-sm btn-warning">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="?sil=<?php echo $firma['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bu firmayı silmek istediğinize emin misiniz?');">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <a href="index.php" class="btn btn-secondary mt-4">
        <i class="bi bi-arrow-left"></i> Ana Sayfa
    </a>
</div>

<!-- Yeni Firma Ekleme Modal -->
<div class="modal fade" id="addServiceModal" tabindex="-1" aria-labelledby="addServiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addServiceModalLabel">Yeni Firma Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Firma Adı <span class="text-danger">*</span></label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Kategori <span class="text-danger">*</span></label>
                            <select name="category" class="form-select" required>
                                <option value="">Seçiniz</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category); ?>"><?php echo htmlspecialchars($category); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">İletişim Kişisi</label>
                            <input type="text" name="contact_person" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Telefon <span class="text-danger">*</span></label>
                            <input type="text" name="phone" class="form-control" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">E-posta</label>
                            <input type="email" name="email" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Adres</label>
                            <input type="text" name="address" class="form-control">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Açıklama</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                        <button type="submit" name="add_service" class="btn btn-success">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Kategori filtreleme
        const filterButtons = document.querySelectorAll('.filter-btn');
        const serviceItems = document.querySelectorAll('.service-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Aktif butonu güncelle
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                
                serviceItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
</body>
</html>
