-- <PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS blocks (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- name sütunu için UNIQUE kısıtlaması ekle
ALTER TABLE blocks ADD CONSTRAINT blocks_name_unique UNIQUE (name);

-- <PERSON><PERSON><PERSON> bloklar
DO $$
BEGIN
    -- A Blok
    IF NOT EXISTS (SELECT 1 FROM blocks WHERE name = 'A Blok') THEN
        INSERT INTO blocks (name, description, created_at) VALUES
        ('A Blok', 'Ana giriş kapısına en yakın blok', NOW());
    END IF;

    -- B Blok
    IF NOT EXISTS (SELECT 1 FROM blocks WHERE name = 'B Blok') THEN
        INSERT INTO blocks (name, description, created_at) VALUES
        ('<PERSON> <PERSON>lok', 'Orta blok', NOW());
    END IF;

    -- C Blok
    IF NOT EXISTS (SELECT 1 FROM blocks WHERE name = 'C Blok') THEN
        INSERT INTO blocks (name, description, created_at) VALUES
        ('C Blok', 'Otopark tarafındaki blok', NOW());
    END IF;
END $$;
