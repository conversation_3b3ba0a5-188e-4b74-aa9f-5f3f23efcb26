<?php
class UserModel {
    private $db;
    public function __construct($db) {
        $this->db = $db;
    }
    public function getAll() {
        $stmt = $this->db->query("SELECT * FROM users ORDER BY id DESC");
        return $stmt->fetchAll();
    }
    public function getById($id) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    public function create($data) {
        $stmt = $this->db->prepare("INSERT INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute([
            $data['username'],
            password_hash($data['password'], PASSWORD_DEFAULT),
            $data['email'],
            $data['full_name'],
            $data['role']
        ]);
    }
    public function update($id, $data) {
        $fields = [
            'username' => $data['username'],
            'email' => $data['email'],
            'full_name' => $data['full_name'],
            'role' => $data['role']
        ];
        $sql = "UPDATE users SET username = :username, email = :email, full_name = :full_name, role = :role";
        if (!empty($data['password'])) {
            $sql .= ", password = :password";
            $fields['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        $sql .= " WHERE id = :id";
        $fields['id'] = $id;
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($fields);
    }
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    }
} 