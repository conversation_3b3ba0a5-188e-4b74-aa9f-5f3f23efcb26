<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $type = $_POST['type'] ?? 'income';
    $amount = trim($_POST['amount'] ?? '');
    $date = trim($_POST['date'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $description = trim($_POST['description'] ?? '');
    if (!$amount || !$date) {
        $error = 'Tutar ve tarih zorunludur!';
    } else {
        if ($type === 'expense') {
            $stmt = $db->prepare('INSERT INTO expenses (amount, date, category, description) VALUES (?, ?, ?, ?)');
            $stmt->execute([$amount, $date, $category, $description]);
            $success = 'Gider hareketi başarıyla eklendi!';
        } else {
            // Gelirler aidat/kira üzerinden kaydedildiği için burada sadece açıklama olarak eklenir
            $stmt = $db->prepare('INSERT INTO payments (amount, payment_date, payment_method, description, created_by) VALUES (?, ?, ?, ?, ?)');
            $stmt->execute([$amount, $date, $category, $description, $user['id']]);
            $success = 'Gelir hareketi başarıyla eklendi!';
        }
        header('Location: kasa.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Yeni Kasa/Banka Hareketi Ekle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:600px;">
    <h2 class="mb-4">Yeni Kasa/Banka Hareketi Ekle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Hareket Türü</label>
            <select name="type" class="form-select" required>
                <option value="income">Gelir</option>
                <option value="expense">Gider</option>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Tutar (TL)</label>
            <input type="number" step="0.01" name="amount" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Tarih</label>
            <input type="date" name="date" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Kategori / Yöntem</label>
            <input type="text" name="category" class="form-control">
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="description" class="form-control" rows="2"></textarea>
        </div>
        <button type="submit" class="btn btn-success">Kaydet</button>
        <a href="kasa.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 