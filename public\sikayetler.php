<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$sikayetler = $db->query('SELECT c.*, u.full_name FROM complaints c
                         LEFT JOIN users u ON c.user_id = u.id
                         ORDER BY c.created_at DESC')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM complaints WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: sikayetler.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON>tler - Apartman <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Şikayetler</h2>
        <a href="sikayet_ekle.php" class="btn btn-success"><i class="bi bi-plus"></i> Yeni Şikayet</a>
    </div>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
        <tr>
            <th>#</th>
            <th>Başlık</th>
            <th>İçerik</th>
            <th>Durum</th>
            <th>Ekleyen</th>
            <th>Tarih</th>
            <th>İşlemler</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($sikayetler as $sikayet): ?>
            <tr>
                <td><?php echo $sikayet['id']; ?></td>
                <td><?php echo htmlspecialchars($sikayet['title']); ?></td>
                <td><?php echo nl2br(htmlspecialchars(substr($sikayet['content'], 0, 100))); ?>
                    <?php if (strlen($sikayet['content']) > 100): ?>...<?php endif; ?>
                </td>
                <td>
                    <?php
                    $status = $sikayet['status'];
                    $statusText = '';
                    $statusClass = '';

                    if ($status === 'pending') {
                        $statusText = 'Beklemede';
                        $statusClass = 'bg-warning';
                    } elseif ($status === 'in_progress') {
                        $statusText = 'İşlemde';
                        $statusClass = 'bg-info';
                    } elseif ($status === 'resolved') {
                        $statusText = 'Çözüldü';
                        $statusClass = 'bg-success';
                    }
                    ?>
                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                </td>
                <td><?php echo htmlspecialchars($sikayet['full_name']); ?></td>
                <td><?php echo date('d.m.Y H:i', strtotime($sikayet['created_at'])); ?></td>
                <td>
                    <a href="sikayet_duzenle.php?id=<?php echo $sikayet['id']; ?>" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                    <a href="?sil=<?php echo $sikayet['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Şikayet silinsin mi?');"><i class="bi bi-trash"></i></a>
                </td>
            </tr>
        <?php endforeach; ?>
        <?php if (empty($sikayetler)): ?>
            <tr>
                <td colspan="7" class="text-center">Henüz şikayet bulunmuyor.</td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-secondary mt-3"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
</div>
</body>
</html>