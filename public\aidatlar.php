<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$aidatlar = $db->query('SELECT d.*, a.number as daire_no, b.name as blok_adi FROM dues d LEFT JOIN apartments a ON d.apartment_id = a.id LEFT JOIN blocks b ON a.block_id = b.id ORDER BY d.id DESC')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM dues WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: aidatlar.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Aidatlar - Apartman <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Aidatlar</h2>
        <a href="aidat_ekle.php" class="btn btn-success"><i class="bi bi-plus"></i> Yeni Aidat</a>
    </div>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
        <tr>
            <th>#</th>
            <th>Blok</th>
            <th>Daire No</th>
            <th>Tutar</th>
            <th>Son Tarih</th>
            <th>Durum</th>
            <th>Ödeme Tarihi</th>
            <th>İşlemler</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($aidatlar as $aidat): ?>
            <tr>
                <td><?php echo $aidat['id']; ?></td>
                <td><?php echo htmlspecialchars($aidat['blok_adi']); ?></td>
                <td><?php echo htmlspecialchars($aidat['daire_no']); ?></td>
                <td><?php echo number_format($aidat['amount'], 2); ?> TL</td>
                <td><?php echo date('d.m.Y', strtotime($aidat['due_date'])); ?></td>
                <td>
                    <?php if ($aidat['status'] === 'paid'): ?>
                        <span class="badge bg-success">Ödendi</span>
                    <?php elseif ($aidat['status'] === 'overdue'): ?>
                        <span class="badge bg-danger">Gecikmiş</span>
                    <?php else: ?>
                        <span class="badge bg-warning text-dark">Ödenmedi</span>
                    <?php endif; ?>
                </td>
                <td><?php echo $aidat['payment_date'] ? date('d.m.Y', strtotime($aidat['payment_date'])) : '-'; ?></td>
                <td>
                    <a href="aidat_duzenle.php?id=<?php echo $aidat['id']; ?>" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                    <a href="?sil=<?php echo $aidat['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Aidat silinsin mi?');"><i class="bi bi-trash"></i></a>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-secondary mt-3"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
</div>
</body>
</html> 