<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();
// Gelirler: aidat ve kira ödemeleri
$incomes = $db->query("SELECT 'Aidat' as type, amount, payment_date as date, 'Aidat Ödemesi' as description FROM dues WHERE status='paid' AND payment_date IS NOT NULL UNION ALL SELECT 'Kira' as type, amount, payment_date as date, description FROM payments").fetchAll(PDO::FETCH_ASSOC);
// Giderler
$expenses = $db->query("SELECT * FROM expenses ORDER BY date DESC").fetchAll(PDO::FETCH_ASSOC);
// Toplamlar
$total_income = 0;
foreach ($incomes as $i) $total_income += (float)$i['amount'];
$total_expense = 0;
foreach ($expenses as $e) $total_expense += (float)$e['amount'];
$balance = $total_income - $total_expense;
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Kasa / Banka Hareketleri</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Kasa / Banka Hareketleri</h2>
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-bg-success mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Gelir</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_income,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-bg-danger mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Gider</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_expense,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-bg-primary mb-3">
                <div class="card-body">
                    <h5 class="card-title">Bakiye</h5>
                    <p class="card-text fs-4"><?php echo number_format($balance,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <h4>Gelirler</h4>
            <table class="table table-bordered table-hover align-middle">
                <thead class="table-light">
                    <tr><th>Tarih</th><th>Tutar</th><th>Tür</th><th>Açıklama</th></tr>
                </thead>
                <tbody>
                <?php foreach ($incomes as $i): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($i['date']); ?></td>
                        <td><?php echo number_format($i['amount'],2,',','.'); ?> ₺</td>
                        <td><?php echo htmlspecialchars($i['type']); ?></td>
                        <td><?php echo htmlspecialchars($i['description']); ?></td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <div class="col-md-6">
            <h4>Giderler</h4>
            <table class="table table-bordered table-hover align-middle">
                <thead class="table-light">
                    <tr><th>Tarih</th><th>Tutar</th><th>Kategori</th><th>Açıklama</th></tr>
                </thead>
                <tbody>
                <?php foreach ($expenses as $e): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($e['date']); ?></td>
                        <td><?php echo number_format($e['amount'],2,',','.'); ?> ₺</td>
                        <td><?php echo htmlspecialchars($e['category'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($e['description']); ?></td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <a href="kasa_hareket_ekle.php" class="btn btn-success mt-3">Yeni Hareket Ekle</a>
</div>
</body>
</html> 