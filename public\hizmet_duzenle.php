<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();

// ID kontrolü
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: hizmetler.php');
    exit;
}

$id = (int)$_GET['id'];
$firma = $db->prepare('SELECT * FROM services WHERE id = ?');
$firma->execute([$id]);
$firma = $firma->fetch();

if (!$firma) {
    header('Location: hizmetler.php');
    exit;
}

// Kategori listesi
$categories = [
    'Elektrik',
    'Su Tesisatı',
    'İnşaat',
    'Temizlik',
    'Güvenlik',
    'Asansör',
    'Ba<PERSON>çe Bakımı',
    'Isıtma-Soğutma',
    'Diğer'
];

// Firma güncelleme
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $contact_person = trim($_POST['contact_person'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (!$name || !$category || !$phone) {
        $error = 'Firma adı, kategori ve telefon zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE services SET name = ?, category = ?, contact_person = ?, phone = ?, email = ?, address = ?, description = ? WHERE id = ?');
        $stmt->execute([$name, $category, $contact_person, $phone, $email, $address, $description, $id]);
        $success = 'Firma bilgileri başarıyla güncellendi!';
        
        // Güncel bilgileri al
        $firma = $db->prepare('SELECT * FROM services WHERE id = ?');
        $firma->execute([$id]);
        $firma = $firma->fetch();
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Firma Düzenle - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Firma Düzenle</h2>
        <a href="hizmetler.php" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Geri Dön
        </a>
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-body">
            <form method="post">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Firma Adı <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($firma['name']); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Kategori <span class="text-danger">*</span></label>
                        <select name="category" class="form-select" required>
                            <option value="">Seçiniz</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo htmlspecialchars($category); ?>" <?php if ($firma['category'] === $category) echo 'selected'; ?>>
                                    <?php echo htmlspecialchars($category); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">İletişim Kişisi</label>
                        <input type="text" name="contact_person" class="form-control" value="<?php echo htmlspecialchars($firma['contact_person']); ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Telefon <span class="text-danger">*</span></label>
                        <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($firma['phone']); ?>" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">E-posta</label>
                        <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($firma['email']); ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Adres</label>
                        <input type="text" name="address" class="form-control" value="<?php echo htmlspecialchars($firma['address']); ?>">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Açıklama</label>
                    <textarea name="description" class="form-control" rows="3"><?php echo htmlspecialchars($firma['description']); ?></textarea>
                </div>
                <div class="text-end">
                    <a href="hizmetler.php" class="btn btn-secondary">İptal</a>
                    <button type="submit" class="btn btn-primary">Güncelle</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
