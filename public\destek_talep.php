<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();

$db = getDBConnection();
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $message = trim($_POST['message'] ?? '');
    if (!$title || !$message) {
        $error = 'Başlık ve açıklama zorunludur!';
    } else {
        $stmt = $db->prepare('INSERT INTO support_requests (user_id, title, message, created_at) VALUES (?, ?, ?, NOW())');
        $stmt->execute([$user['id'], $title, $message]);
        $success = 'Destek talebiniz başarıyla gönderildi!';
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Destek <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:700px;">
    <h2 class="mb-4">Destek Talebi Gönder</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Başlık</label>
            <input type="text" name="title" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="message" class="form-control" rows="5" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Gönder</button>
    </form>
</div>
</body>
</html> 