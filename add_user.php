<?php
// Veritabanı bağlantı bilgileri
$host = 'localhost';
$port = '5434';
$dbname = 'AYSDATA';
$user = 'postgres';
$password = 'postgres'; // PostgreSQL şifrenizi buraya girin

try {
    // PostgreSQL veritabanına bağlan
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Kullanıcı bilgileri
    $username = 'admin';
    $email = '<EMAIL>';
    $full_name = 'Admin Kullanıcı';
    $role = 'admin';

    // Şifreyi güvenli bir şekilde hashle
    $password = password_hash('password', PASSWORD_DEFAULT);

    // Kullanıcının zaten var olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $userExists = $stmt->fetchColumn() > 0;

    if ($userExists) {
        echo "Bu kullanıcı adı zaten kullanılıyor!";
    } else {
        // Kullanıcıyı ekle
        $stmt = $pdo->prepare("INSERT INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$username, $password, $email, $full_name, $role]);

        echo "Kullanıcı başarıyla eklendi!<br>";
        echo "Kullanıcı adı: $username<br>";
        echo "Şifre: password<br>";
        echo "Rol: $role<br>";
    }
} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage();
}
?>
