<?php
require_once '../config/functions.php';
requireLogin();
requireRole('admin');

$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $role = $_POST['role'] ?? 'resident';

    if (!$full_name || !$username || !$email || !$password) {
        $error = 'Tüm alanları doldurun!';
    } else {
        $db = getDBConnection();
        $stmt = $db->prepare('SELECT COUNT(*) FROM users WHERE username = ? OR email = ?');
        $stmt->execute([$username, $email]);
        if ($stmt->fetchColumn() > 0) {
            $error = 'Bu kullanıcı adı veya e-posta zaten kayıtlı!';
        } else {
            $stmt = $db->prepare('INSERT INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)');
            $stmt->execute([
                $username,
                password_hash($password, PASSWORD_DEFAULT),
                $email,
                $full_name,
                $role
            ]);
            header('Location: kullanicilar.php');
            exit;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Yeni Kullanıcı Ekle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Yeni Kullanıcı Ekle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">İsim Soyisim</label>
            <input type="text" name="full_name" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Kullanıcı Adı</label>
            <input type="text" name="username" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">E-Posta</label>
            <input type="email" name="email" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Şifre</label>
            <input type="password" name="password" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Rol</label>
            <select name="role" class="form-select">
                <option value="admin">Admin</option>
                <option value="manager">Yönetici</option>
                <option value="resident" selected>Sakin</option>
            </select>
        </div>
        <button type="submit" class="btn btn-success">Kaydet</button>
        <a href="kullanicilar.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 