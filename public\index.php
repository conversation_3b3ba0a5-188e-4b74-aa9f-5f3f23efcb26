<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
// Özet kutuları için örne<PERSON> veriler (modüller tamamlandıkça sorgular eklenecek)
$toplam_aidat = 422;
$odenen_aidat = 195;
$odenmeyen_aidat = 227;
$toplam_kira = 7219;
$odenen_kira = 750;
$odenmeyen_kira = 6469;
$toplam_gider = 5478;
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Apartman <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }

        .dashboard-card {
            min-height: 160px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .dashboard-card:hover::before {
            opacity: 1;
        }

        .dashboard-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover .dashboard-icon {
            transform: scale(1.1);
            opacity: 1;
        }

        .dashboard-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .summary-box {
            border-radius: 15px;
            color: #fff;
            padding: 1.5rem 2rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        }

        .summary-box:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.2);
        }

        .summary-icon {
            font-size: 2.5rem;
            opacity: 0.9;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-modern {
            border-radius: 12px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Modern gradient colors for cards */
        .card-gradient-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-gradient-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .card-gradient-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .card-gradient-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .card-gradient-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .card-gradient-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .card-gradient-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .card-gradient-8 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }
        .card-gradient-9 { background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%); }
        .card-gradient-10 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .card-gradient-11 { background: linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%); }
        .card-gradient-12 { background: linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%); }
        .card-gradient-13 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        .card-gradient-14 { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }
        .card-gradient-15 { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); }
        .card-gradient-16 { background: linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%); }

        .divider-modern {
            border: none;
            height: 3px;
            background: linear-gradient(90deg, transparent, #667eea, #764ba2, transparent);
            margin: 3rem 0;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header-section">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">Merhaba, <b><?php echo htmlspecialchars($user['full_name']); ?></b></h1>
                        <p class="mb-0 opacity-75">Apartman Yönetim Sistemi'ne hoş geldiniz</p>
                    </div>
                    <div>
                        <a href="switch_user.php" class="btn btn-light btn-modern me-3">
                            <i class="bi bi-people"></i> Kullanıcı Değiştir
                        </a>
                        <a href="logout.php" class="btn btn-outline-light btn-modern">
                            <i class="bi bi-box-arrow-right"></i> Çıkış Yap
                        </a>
                    </div>
                </div>
            </div>
        <!-- Özet Kutuları -->
        <div class="row mb-4 g-3">
            <div class="col-12 col-md-3">
                <div class="summary-box bg-primary">
                    <span class="summary-icon"><i class="bi bi-cash-stack"></i></span>
                    <div>
                        <div>Toplam Aidat</div>
                        <div class="fw-bold fs-4"><?php echo $toplam_aidat; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="summary-box bg-success">
                    <span class="summary-icon"><i class="bi bi-check-circle"></i></span>
                    <div>
                        <div>Ödenen Aidat</div>
                        <div class="fw-bold fs-4"><?php echo $odenen_aidat; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="summary-box bg-danger">
                    <span class="summary-icon"><i class="bi bi-x-circle"></i></span>
                    <div>
                        <div>Ödenmeyen Aidat</div>
                        <div class="fw-bold fs-4"><?php echo $odenmeyen_aidat; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-3">
                <div class="summary-box bg-info">
                    <span class="summary-icon"><i class="bi bi-currency-dollar"></i></span>
                    <div>
                        <div>Toplam Kira</div>
                        <div class="fw-bold fs-4"><?php echo $toplam_kira; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="summary-box bg-success">
                    <span class="summary-icon"><i class="bi bi-check2-circle"></i></span>
                    <div>
                        <div>Ödenen Kira</div>
                        <div class="fw-bold fs-4"><?php echo $odenen_kira; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="summary-box bg-danger">
                    <span class="summary-icon"><i class="bi bi-x-octagon"></i></span>
                    <div>
                        <div>Ödenmeyen Kira</div>
                        <div class="fw-bold fs-4"><?php echo $odenmeyen_kira; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-3">
                <div class="summary-box bg-warning">
                    <span class="summary-icon"><i class="bi bi-cash-coin"></i></span>
                    <div>
                        <div>Toplam Gider</div>
                        <div class="fw-bold fs-4"><?php echo $toplam_gider; ?> TL</div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="divider-modern">

        <!-- Dashboard Kartları -->
        <div class="row g-4">

            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="daireler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-1 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-house-door"></i></div>
                        Daireler
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="aidatlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-2 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-cash-stack"></i></div>
                        Aidatlar
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="kiralar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-3 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-currency-dollar"></i></div>
                        Kiralar
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="duyurular.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-4 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-bell"></i></div>
                        Duyurular
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="sikayetler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-5 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-exclamation-triangle"></i></div>
                        Şikayetler
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="destek_talepleri.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-6 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-chat-dots"></i></div>
                        İstek ve Öneriler
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="hizmetler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-7 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-tools"></i></div>
                        Hizmetler-Firmalar
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="raporlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-8 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-bar-chart"></i></div>
                        Raporlar & İstatistikler
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="kullanicilar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-9 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-people"></i></div>
                        Kullanıcılar
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="notlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-10 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-journal"></i></div>
                        Notlar
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="calisanlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-11 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-person-badge"></i></div>
                        Çalışanlar
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="giderler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-12 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-cash-coin"></i></div>
                        Giderler
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="ayarlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-13 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-person-circle"></i></div>
                        Profil
                    </div>
                </a>
            </div>
            <?php if ($user['role'] === 'admin'): ?>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="yedekleme.php" class="dashboard-link">
                    <div class="dashboard-card bg-dark text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-cloud-download"></i></div>
                        Yedekleme
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                <a href="sql_manager.php" class="dashboard-link">
                    <div class="dashboard-card bg-dark text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-database-gear"></i></div>
                        SQL Yöneticisi
                    </div>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>