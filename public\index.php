<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
// Özet kutuları için örnek veriler (modüller tamamlandıkça sorgular eklenecek)
$toplam_aidat = 422;
$odenen_aidat = 195;
$odenmeyen_aidat = 227;
$toplam_kira = 7219;
$odenen_kira = 750;
$odenmeyen_kira = 6469;
$toplam_gider = 5478;
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="Modern Apartman Yönetim Sistemi - Responsive ve kullanıcı dostu arayüz">
    <meta name="theme-color" content="#667eea">
    <title>Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }

        .dashboard-card {
            min-height: 160px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .dashboard-card:hover::before {
            opacity: 1;
        }

        .dashboard-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover .dashboard-icon {
            transform: scale(1.1);
            opacity: 1;
        }

        .dashboard-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .summary-box {
            border-radius: 15px;
            color: #fff;
            padding: 1.5rem 2rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        }

        .summary-box:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.2);
        }

        .summary-icon {
            font-size: 2.5rem;
            opacity: 0.9;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-modern {
            border-radius: 12px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Modern gradient colors for cards */
        .card-gradient-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-gradient-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .card-gradient-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .card-gradient-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .card-gradient-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .card-gradient-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .card-gradient-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .card-gradient-8 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }
        .card-gradient-9 { background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%); }
        .card-gradient-10 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .card-gradient-11 { background: linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%); }
        .card-gradient-12 { background: linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%); }
        .card-gradient-13 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        .card-gradient-14 { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }
        .card-gradient-15 { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); }
        .card-gradient-16 { background: linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%); }

        .divider-modern {
            border: none;
            height: 3px;
            background: linear-gradient(90deg, transparent, #667eea, #764ba2, transparent);
            margin: 3rem 0;
            border-radius: 2px;
        }

        /* Responsive Design - Mobile First Approach */
        @media (max-width: 576px) {
            .main-container {
                margin: 10px;
                padding: 15px;
                border-radius: 15px;
            }

            .header-section {
                padding: 1.5rem 1rem;
                border-radius: 15px;
                margin-bottom: 1.5rem;
                text-align: center;
            }

            .header-section .d-flex {
                flex-direction: column;
                gap: 1rem;
            }

            .header-section h1 {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .header-section p {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }

            .btn-modern {
                padding: 8px 16px;
                font-size: 0.9rem;
                margin: 0.25rem;
            }

            .summary-box {
                padding: 1rem 1.5rem;
                margin-bottom: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .summary-icon {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            .dashboard-card {
                min-height: 120px;
                font-size: 1rem;
                padding: 1rem;
            }

            .dashboard-icon {
                font-size: 2.5rem;
                margin-bottom: 10px;
            }

            .divider-modern {
                margin: 2rem 0;
                height: 2px;
            }

            .row.g-4 {
                --bs-gutter-x: 1rem;
                --bs-gutter-y: 1rem;
            }
        }

        /* Tablet Portrait */
        @media (min-width: 577px) and (max-width: 768px) {
            .main-container {
                margin: 15px;
                padding: 20px;
            }

            .header-section {
                padding: 1.75rem;
            }

            .header-section h1 {
                font-size: 1.75rem;
            }

            .summary-box {
                padding: 1.25rem 1.75rem;
            }

            .dashboard-card {
                min-height: 140px;
                font-size: 1.05rem;
            }

            .dashboard-icon {
                font-size: 2.75rem;
            }
        }

        /* Tablet Landscape */
        @media (min-width: 769px) and (max-width: 992px) {
            .main-container {
                margin: 20px;
                padding: 25px;
            }

            .header-section {
                padding: 2rem;
            }

            .summary-box {
                padding: 1.4rem 1.8rem;
            }

            .dashboard-card {
                min-height: 150px;
            }
        }

        /* Desktop */
        @media (min-width: 993px) {
            .main-container {
                max-width: 1400px;
                margin: 20px auto;
            }
        }

        /* Large Desktop */
        @media (min-width: 1200px) {
            .main-container {
                max-width: 1600px;
            }

            .dashboard-card {
                min-height: 170px;
                font-size: 1.2rem;
            }

            .dashboard-icon {
                font-size: 3.2rem;
            }
        }

        /* Touch Device Optimizations */
        @media (hover: none) and (pointer: coarse) {
            .dashboard-card:hover {
                transform: none;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }

            .dashboard-card:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }

            .summary-box:hover {
                transform: none;
            }

            .btn-modern:hover {
                transform: none;
            }

            .btn-modern:active {
                transform: scale(0.95);
            }
        }

        /* High DPI Displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .dashboard-card {
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

            .summary-box {
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }
        }

        /* Landscape Orientation for Mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .header-section {
                padding: 1rem;
            }

            .header-section h1 {
                font-size: 1.4rem;
            }

            .summary-box {
                padding: 0.8rem 1.2rem;
                flex-direction: row;
                text-align: left;
            }

            .dashboard-card {
                min-height: 100px;
                font-size: 0.95rem;
            }

            .dashboard-icon {
                font-size: 2.2rem;
                margin-bottom: 5px;
            }
        }

        /* Accessibility Improvements */
        @media (prefers-reduced-motion: reduce) {
            .dashboard-card,
            .summary-box,
            .btn-modern,
            .dashboard-icon {
                transition: none;
            }

            .dashboard-card:hover {
                transform: none;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            .main-container {
                background: rgba(30, 30, 30, 0.95);
                color: #ffffff;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header-section">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">Merhaba, <b><?php echo htmlspecialchars($user['full_name']); ?></b></h1>
                        <p class="mb-0 opacity-75">Apartman Yönetim Sistemi'ne hoş geldiniz</p>
                    </div>
                    <div>
                        <a href="switch_user.php" class="btn btn-light btn-modern me-3">
                            <i class="bi bi-people"></i> Kullanıcı Değiştir
                        </a>
                        <a href="logout.php" class="btn btn-outline-light btn-modern">
                            <i class="bi bi-box-arrow-right"></i> Çıkış Yap
                        </a>
                    </div>
                </div>
            </div>
        <!-- Özet Kutuları -->
        <div class="row mb-4 g-3">
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-1">
                    <span class="summary-icon"><i class="bi bi-cash-stack"></i></span>
                    <div>
                        <div>Toplam Aidat</div>
                        <div class="fw-bold fs-4"><?php echo $toplam_aidat; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-4">
                    <span class="summary-icon"><i class="bi bi-check-circle"></i></span>
                    <div>
                        <div>Ödenen Aidat</div>
                        <div class="fw-bold fs-4"><?php echo $odenen_aidat; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-2">
                    <span class="summary-icon"><i class="bi bi-x-circle"></i></span>
                    <div>
                        <div>Ödenmeyen Aidat</div>
                        <div class="fw-bold fs-4"><?php echo $odenmeyen_aidat; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-3">
                    <span class="summary-icon"><i class="bi bi-currency-dollar"></i></span>
                    <div>
                        <div>Toplam Kira</div>
                        <div class="fw-bold fs-4"><?php echo $toplam_kira; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-12">
                    <span class="summary-icon"><i class="bi bi-check2-circle"></i></span>
                    <div>
                        <div>Ödenen Kira</div>
                        <div class="fw-bold fs-4"><?php echo $odenen_kira; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-5">
                    <span class="summary-icon"><i class="bi bi-x-octagon"></i></span>
                    <div>
                        <div>Ödenmeyen Kira</div>
                        <div class="fw-bold fs-4"><?php echo $odenmeyen_kira; ?> TL</div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="summary-box card-gradient-15">
                    <span class="summary-icon"><i class="bi bi-cash-coin"></i></span>
                    <div>
                        <div>Toplam Gider</div>
                        <div class="fw-bold fs-4"><?php echo $toplam_gider; ?> TL</div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="divider-modern">

        <!-- Dashboard Kartları -->
        <div class="row g-3 g-md-4">

            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="daireler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-1 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-house-door"></i></div>
                        Daireler
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="aidatlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-2 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-cash-stack"></i></div>
                        Aidatlar
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="kiralar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-3 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-currency-dollar"></i></div>
                        Kiralar
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="duyurular.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-4 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-bell"></i></div>
                        Duyurular
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="sikayetler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-5 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-exclamation-triangle"></i></div>
                        Şikayetler
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="destek_talepleri.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-6 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-chat-dots"></i></div>
                        İstek ve Öneriler
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="hizmetler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-7 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-tools"></i></div>
                        Hizmetler-Firmalar
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="raporlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-8 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-bar-chart"></i></div>
                        Raporlar & İstatistikler
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="kullanicilar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-9 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-people"></i></div>
                        Kullanıcılar
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="notlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-10 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-journal"></i></div>
                        Notlar
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="calisanlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-11 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-person-badge"></i></div>
                        Çalışanlar
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="giderler.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-12 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-cash-coin"></i></div>
                        Giderler
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="ayarlar.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-13 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-person-circle"></i></div>
                        Profil
                    </div>
                </a>
            </div>
            <?php if ($user['role'] === 'admin'): ?>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="yedekleme.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-14 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-cloud-download"></i></div>
                        Yedekleme
                    </div>
                </a>
            </div>
            <div class="col-6 col-sm-6 col-md-4 col-lg-3 col-xl-2">
                <a href="sql_manager.php" class="dashboard-link">
                    <div class="dashboard-card card-gradient-15 text-white text-center">
                        <div class="dashboard-icon"><i class="bi bi-database-gear"></i></div>
                        SQL Yöneticisi
                    </div>
                </a>
            </div>
            <?php endif; ?>
        </div>
        </div>
    </div>
</body>
</html>