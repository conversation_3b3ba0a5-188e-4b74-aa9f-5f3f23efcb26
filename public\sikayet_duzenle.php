<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: sikayetler.php');
    exit;
}
$id = (int)$_GET['id'];
$sikayet = $db->prepare('SELECT * FROM complaints WHERE id = ?');
$sikayet->execute([$id]);
$sikayet = $sikayet->fetch();
if (!$sikayet) {
    header('Location: sikayetler.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $status = $_POST['status'] ?? 'pending';
    if (!$title || !$content) {
        $error = 'Başlık ve içerik zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE complaints SET title=?, content=?, status=? WHERE id=?');
        $stmt->execute([$title, $content, $status, $id]);
        header('Location: sikayetler.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Şikayet Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:800px;">
    <h2 class="mb-4">Şikayet Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Başlık</label>
            <input type="text" name="title" class="form-control" value="<?php echo htmlspecialchars($sikayet['title']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">İçerik</label>
            <textarea name="content" class="form-control" rows="5" required><?php echo htmlspecialchars($sikayet['content']); ?></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">Durum</label>
            <select name="status" class="form-select">
                <option value="pending" <?php if($sikayet['status']==='pending') echo 'selected'; ?>>Beklemede</option>
                <option value="in_progress" <?php if($sikayet['status']==='in_progress') echo 'selected'; ?>>İşlemde</option>
                <option value="resolved" <?php if($sikayet['status']==='resolved') echo 'selected'; ?>>Çözüldü</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="sikayetler.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 