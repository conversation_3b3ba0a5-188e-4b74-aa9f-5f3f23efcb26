<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: index.php');
    exit;
}

$success = '';
$error = '';

// Veritabanı bağlantı bilgilerini config.php'den al
require_once '../config/config.php';

// pg_dump ile yedekleme
if (isset($_POST['backup'])) {
    // PostgreSQL dump komutu
    $db_name = DB_NAME;
    $db_user = DB_USER;
    $db_host = DB_HOST;
    $db_port = DB_PORT;
    $db_pass = DB_PASS;

    // Yedek dosyası için benzersiz isim oluştur
    $backup_file = sys_get_temp_dir() . "/AYSDATA_backup_" . date('Ymd_His') . ".sql";

    // Hata ayıklama bilgisi
    $debug_info = "";
    $debug_info .= "DB Host: $db_host\n";
    $debug_info .= "DB Port: $db_port\n";
    $debug_info .= "DB Name: $db_name\n";
    $debug_info .= "DB User: $db_user\n";
    $debug_info .= "Temp File: $backup_file\n";

    try {
        // PGPASSWORD çevre değişkenini ayarla
        putenv("PGPASSWORD=$db_pass");

        // pg_dump komutunu oluştur
        $cmd = "pg_dump -h $db_host -p $db_port -U $db_user -F p $db_name > \"$backup_file\" 2>&1";

        // Komutu çalıştır ve çıktıyı al
        exec($cmd, $output, $retval);
        $cmd_output = implode("\n", $output);

        if ($retval === 0 && file_exists($backup_file) && filesize($backup_file) > 0) {
            // Başarılı - dosyayı indir
            header('Content-Type: application/sql');
            header('Content-Disposition: attachment; filename="' . basename($backup_file) . '"');
            readfile($backup_file);
            unlink($backup_file);
            exit;
        } else {
            // Hata - detaylı hata mesajı göster
            $error = "Yedek alınırken hata oluştu! Hata kodu: $retval";
            $error .= "<br>Komut çıktısı: " . htmlspecialchars($cmd_output);
            $error .= "<br><br>Debug bilgisi:<br><pre>" . htmlspecialchars($debug_info) . "</pre>";
        }
    } catch (Exception $e) {
        $error = "Yedek alınırken bir istisna oluştu: " . $e->getMessage();
    }
}

// PHP ile yedekleme
if (isset($_POST['backup_php'])) {
    try {
        // Veritabanına bağlan
        $dsn = "pgsql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Tüm tabloları al
        $tables = [];
        $result = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE'");
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            $tables[] = $row['table_name'];
        }

        // SQL dosyası başlat
        $sql = "-- AYSDATA Veritabanı Yedeği\n";
        $sql .= "-- Oluşturulma Tarihi: " . date('Y-m-d H:i:s') . "\n\n";

        // Her tablo için
        foreach ($tables as $table) {
            $sql .= "-- Tablo: $table\n";

            // Tablo yapısını al
            $result = $pdo->query("SELECT column_name, data_type, character_maximum_length, is_nullable, column_default
                                  FROM information_schema.columns
                                  WHERE table_name = '$table'
                                  ORDER BY ordinal_position");

            $columns = [];
            $sql .= "\nCREATE TABLE IF NOT EXISTS $table (\n";
            $columnDefs = [];

            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $row['column_name'];

                $columnDef = "    " . $row['column_name'] . " " . $row['data_type'];

                if ($row['character_maximum_length']) {
                    $columnDef .= "(" . $row['character_maximum_length'] . ")";
                }

                if ($row['is_nullable'] === 'NO') {
                    $columnDef .= " NOT NULL";
                }

                if ($row['column_default']) {
                    $columnDef .= " DEFAULT " . $row['column_default'];
                }

                $columnDefs[] = $columnDef;
            }

            $sql .= implode(",\n", $columnDefs);
            $sql .= "\n);\n\n";

            // Tablo verilerini al
            $result = $pdo->query("SELECT * FROM $table");
            $rows = $result->fetchAll(PDO::FETCH_ASSOC);

            if (count($rows) > 0) {
                $sql .= "-- Tablo verileri: $table\n";

                foreach ($rows as $row) {
                    $values = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $values[] = "NULL";
                        } elseif (is_numeric($value)) {
                            $values[] = $value;
                        } else {
                            $values[] = "'" . str_replace("'", "''", $value) . "'";
                        }
                    }

                    $sql .= "INSERT INTO $table (" . implode(", ", $columns) . ") VALUES (" . implode(", ", $values) . ");\n";
                }

                $sql .= "\n";
            }
        }

        // Dosyayı indir
        $filename = "AYSDATA_PHP_backup_" . date('Ymd_His') . ".sql";
        header('Content-Type: application/sql');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo $sql;
        exit;
    } catch (Exception $e) {
        $error = "PHP ile yedek alınırken hata oluştu: " . $e->getMessage();
    }
}

// Yedekten Geri Yükleme
if (isset($_POST['restore']) && isset($_FILES['restore_file']) && $_FILES['restore_file']['error'] === UPLOAD_ERR_OK) {
    // Veritabanı bağlantı bilgilerini config.php'den al
    require_once '../config/config.php';

    // PostgreSQL restore komutu
    $db_name = DB_NAME;
    $db_user = DB_USER;
    $db_host = DB_HOST;
    $db_port = DB_PORT;
    $db_pass = DB_PASS;

    // Yüklenen dosya
    $restore_file = $_FILES['restore_file']['tmp_name'];

    // Hata ayıklama bilgisi
    $debug_info = "";
    $debug_info .= "DB Host: $db_host\n";
    $debug_info .= "DB Port: $db_port\n";
    $debug_info .= "DB Name: $db_name\n";
    $debug_info .= "DB User: $db_user\n";
    $debug_info .= "Restore File: $restore_file\n";
    $debug_info .= "File Size: " . filesize($restore_file) . " bytes\n";

    try {
        // PGPASSWORD çevre değişkenini ayarla
        putenv("PGPASSWORD=$db_pass");

        // psql komutunu oluştur
        $cmd = "psql -h $db_host -p $db_port -U $db_user -d $db_name < \"$restore_file\" 2>&1";

        // Komutu çalıştır ve çıktıyı al
        exec($cmd, $output, $retval);
        $cmd_output = implode("\n", $output);

        if ($retval === 0) {
            // Başarılı
            $success = 'Yedekten geri yükleme başarılı!';
        } else {
            // Hata - detaylı hata mesajı göster
            $error = "Yedekten geri yükleme sırasında hata oluştu! Hata kodu: $retval";
            $error .= "<br>Komut çıktısı: " . htmlspecialchars($cmd_output);
            $error .= "<br><br>Debug bilgisi:<br><pre>" . htmlspecialchars($debug_info) . "</pre>";
        }
    } catch (Exception $e) {
        $error = 'Yedekten geri yükleme sırasında bir istisna oluştu: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Yedekleme & Geri Yükleme</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:700px;">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Yedekleme & Geri Yükleme</h2>
        <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
    </div>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <!-- PostgreSQL Durum Kontrolü -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <i class="bi bi-info-circle"></i> PostgreSQL Durum Kontrolü
        </div>
        <div class="card-body">
            <?php
            // PostgreSQL komut satırı araçlarının varlığını kontrol et
            $pg_dump_exists = false;
            $psql_exists = false;

            // Windows'ta komut kontrolü
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                exec('where pg_dump', $output_dump, $return_dump);
                exec('where psql', $output_psql, $return_psql);

                $pg_dump_exists = ($return_dump === 0);
                $psql_exists = ($return_psql === 0);
            } else {
                // Linux/Unix/Mac'te komut kontrolü
                exec('which pg_dump', $output_dump, $return_dump);
                exec('which psql', $output_psql, $return_psql);

                $pg_dump_exists = ($return_dump === 0);
                $psql_exists = ($return_psql === 0);
            }
            ?>

            <div class="mb-3">
                <strong>pg_dump Durumu:</strong>
                <?php if ($pg_dump_exists): ?>
                    <span class="badge bg-success">Mevcut</span>
                <?php else: ?>
                    <span class="badge bg-danger">Bulunamadı</span>
                    <div class="alert alert-warning mt-2">
                        <i class="bi bi-exclamation-triangle"></i> pg_dump komutu bulunamadı. Yedek alma işlemi çalışmayabilir.
                        <br>PostgreSQL komut satırı araçlarının yüklü olduğundan ve PATH'e eklendiğinden emin olun.
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <strong>psql Durumu:</strong>
                <?php if ($psql_exists): ?>
                    <span class="badge bg-success">Mevcut</span>
                <?php else: ?>
                    <span class="badge bg-danger">Bulunamadı</span>
                    <div class="alert alert-warning mt-2">
                        <i class="bi bi-exclamation-triangle"></i> psql komutu bulunamadı. Geri yükleme işlemi çalışmayabilir.
                        <br>PostgreSQL komut satırı araçlarının yüklü olduğundan ve PATH'e eklendiğinden emin olun.
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <strong>Veritabanı Bağlantı Bilgileri:</strong>
                <ul class="list-group mt-2">
                    <li class="list-group-item">Host: <?php echo DB_HOST; ?></li>
                    <li class="list-group-item">Port: <?php echo DB_PORT; ?></li>
                    <li class="list-group-item">Veritabanı: <?php echo DB_NAME; ?></li>
                    <li class="list-group-item">Kullanıcı: <?php echo DB_USER; ?></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <i class="bi bi-download"></i> Veritabanı Yedeği Al
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Yöntem 1: pg_dump ile Yedek Al</h5>
                    <p class="text-muted">PostgreSQL komut satırı aracı pg_dump kullanarak yedek alır.</p>
                    <form method="post">
                        <button type="submit" name="backup" class="btn btn-primary">
                            <i class="bi bi-download"></i> pg_dump ile Yedek Al
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <h5>Yöntem 2: PHP ile Yedek Al</h5>
                    <p class="text-muted">PHP kullanarak veritabanı yapısını ve verilerini dışa aktarır.</p>
                    <form method="post">
                        <button type="submit" name="backup_php" class="btn btn-success">
                            <i class="bi bi-download"></i> PHP ile Yedek Al
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="bi bi-upload"></i> Yedekten Geri Yükleme
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle-fill"></i> <strong>Uyarı:</strong> Yedekten geri yükleme işlemi mevcut veritabanı verilerini geri döndürülemez şekilde değiştirebilir. Lütfen dikkatli kullanın!
            </div>
            <form method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label class="form-label">Yedek Dosyası Yükle (.sql)</label>
                    <input type="file" name="restore_file" class="form-control" accept=".sql" required>
                </div>
                <button type="submit" name="restore" class="btn btn-danger">
                    <i class="bi bi-upload"></i> Yedekten Geri Yükle
                </button>
            </form>
        </div>
    </div>
</div>
</body>
</html>