<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: apartmanlar.php');
    exit;
}
$id = (int)$_GET['id'];
$apartman = $db->prepare('SELECT * FROM blocks WHERE id = ?');
$apartman->execute([$id]);
$apartman = $apartman->fetch();
if (!$apartman) {
    header('Location: apartmanlar.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    if (!$name) {
        $error = 'Apartman adı zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE blocks SET name=?, description=? WHERE id=?');
        $stmt->execute([$name, $description, $id]);
        header('Location: apartmanlar.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Apartman Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Apartman Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Apartman Adı</label>
            <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($apartman['name']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="description" class="form-control" rows="3"><?php echo htmlspecialchars($apartman['description']); ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="apartmanlar.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 