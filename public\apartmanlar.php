<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$apartmanlar = $db->query('SELECT * FROM blocks ORDER BY id DESC')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM blocks WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: apartmanlar.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Apartmanlar - Apartman Yönet<PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Apartmanlar</h2>
        <a href="apartman_ekle.php" class="btn btn-success"><i class="bi bi-plus"></i> Yeni Apartman</a>
    </div>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
        <tr>
            <th>#</th>
            <th>Apartman Adı</th>
            <th>Açıklama</th>
            <th>Oluşturulma</th>
            <th>İşlemler</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($apartmanlar as $apartman): ?>
            <tr>
                <td><?php echo $apartman['id']; ?></td>
                <td><?php echo htmlspecialchars($apartman['name']); ?></td>
                <td><?php echo htmlspecialchars($apartman['description']); ?></td>
                <td><?php echo date('d.m.Y', strtotime($apartman['created_at'])); ?></td>
                <td>
                    <a href="apartman_duzenle.php?id=<?php echo $apartman['id']; ?>" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                    <a href="?sil=<?php echo $apartman['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apartman silinsin mi?');"><i class="bi bi-trash"></i></a>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-secondary mt-3"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
</div>
</body>
</html> 