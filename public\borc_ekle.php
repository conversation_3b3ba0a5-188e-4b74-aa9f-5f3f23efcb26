<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$daireler = $db->query('SELECT a.id, a.number, b.name as block_name FROM apartments a LEFT JOIN blocks b ON a.block_id = b.id ORDER BY b.name, a.number')->fetchAll();

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $apartment_id = $_POST['apartment_id'] ?? '';
    $amount = trim($_POST['amount'] ?? '');
    $due_date = trim($_POST['due_date'] ?? '');
    $description = trim($_POST['description'] ?? '');
    if (!$apartment_id || !$amount || !$due_date) {
        $error = 'Daire, tutar ve son ödeme tarihi zorunludur!';
    } else {
        $stmt = $db->prepare('INSERT INTO dues (apartment_id, amount, due_date, description, status) VALUES (?, ?, ?, ?, "unpaid")');
        $stmt->execute([$apartment_id, $amount, $due_date, $description]);
        header('Location: borclar.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Yeni Borç Ekle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Yeni Borç Ekle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Daire</label>
            <select name="apartment_id" class="form-select" required>
                <option value="">Seçiniz</option>
                <?php foreach ($daireler as $daire): ?>
                    <option value="<?php echo $daire['id']; ?>"><?php echo htmlspecialchars($daire['block_name'] . ' - ' . $daire['number']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Tutar (TL)</label>
            <input type="number" step="0.01" name="amount" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Son Ödeme Tarihi</label>
            <input type="date" name="due_date" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="description" class="form-control" rows="2"></textarea>
        </div>
        <button type="submit" class="btn btn-success">Kaydet</button>
        <a href="borclar.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 