<?php
// Genel yapılandırma ve veritabanı bağlantısı
error_reporting(E_ALL);
ini_set('display_errors', 1);
define('DB_HOST', 'localhost');
define('DB_PORT', '5434');
define('DB_NAME', 'AYSDATA');
define('DB_USER', 'postgres');
define('DB_PASS', '5213'); // PostgreSQL şifrenizi buraya girin
define('APP_NAME', 'Apartman Yönet<PERSON>');
define('APP_URL', 'http://localhost/AYSPANEL');
session_start();
date_default_timezone_set('Europe/Istanbul');
function getDBConnection() {
    try {
        $dsn = "pgsql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        die("Veritabanı bağlantı hatası: " . $e->getMessage());
    }
}