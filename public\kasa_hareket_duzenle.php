<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
if (!isset($_GET['type']) || !isset($_GET['id']) || !in_array($_GET['type'], ['income', 'expense']) || !is_numeric($_GET['id'])) {
    header('Location: kasa.php');
    exit;
}
$type = $_GET['type'];
$id = (int)$_GET['id'];

if ($type === 'expense') {
    $stmt = $db->prepare('SELECT * FROM expenses WHERE id = ?');
    $stmt->execute([$id]);
    $hareket = $stmt->fetch();
    if (!$hareket) {
        header('Location: kasa.php');
        exit;
    }
} else {
    $stmt = $db->prepare('SELECT * FROM payments WHERE id = ?');
    $stmt->execute([$id]);
    $hareket = $stmt->fetch();
    if (!$hareket) {
        header('Location: kasa.php');
        exit;
    }
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $amount = trim($_POST['amount'] ?? '');
    $date = trim($_POST['date'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $description = trim($_POST['description'] ?? '');
    if (!$amount || !$date) {
        $error = 'Tutar ve tarih zorunludur!';
    } else {
        if ($type === 'expense') {
            $stmt = $db->prepare('UPDATE expenses SET amount=?, date=?, category=?, description=? WHERE id=?');
            $stmt->execute([$amount, $date, $category, $description, $id]);
        } else {
            $stmt = $db->prepare('UPDATE payments SET amount=?, payment_date=?, payment_method=?, description=? WHERE id=?');
            $stmt->execute([$amount, $date, $category, $description, $id]);
        }
        header('Location: kasa.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Kasa/Banka Hareketi Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:600px;">
    <h2 class="mb-4">Kasa/Banka Hareketi Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Hareket Türü</label>
            <input type="text" class="form-control" value="<?php echo $type === 'expense' ? 'Gider' : 'Gelir'; ?>" disabled>
        </div>
        <div class="mb-3">
            <label class="form-label">Tutar (TL)</label>
            <input type="number" step="0.01" name="amount" class="form-control" value="<?php echo htmlspecialchars($hareket['amount']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Tarih</label>
            <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($type==='expense'?$hareket['date']:$hareket['payment_date']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Kategori / Yöntem</label>
            <input type="text" name="category" class="form-control" value="<?php echo htmlspecialchars($type==='expense'?$hareket['category']:$hareket['payment_method']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="description" class="form-control" rows="2"><?php echo htmlspecialchars($hareket['description']); ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="kasa.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 