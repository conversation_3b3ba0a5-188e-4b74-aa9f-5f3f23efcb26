<?php
// Veritabanı bağlantı bilgileri
$host = 'localhost';
$port = '5434';
$dbname = 'AYSDATA';
$user = 'postgres';
$password = '5213'; // PostgreSQL şifrenizi buraya girin

try {
    // PostgreSQL veritabanına bağlan
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Complaints tablosunu oluştur
    $sql = "
    CREATE TABLE IF NOT EXISTS complaints (
        id SERIAL PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        content TEXT NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'resolved')),
        user_id INTEGER REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    $pdo->exec($sql);
    echo "Complaints tablosu başarıyla oluşturuldu!";

} catch (PDOException $e) {
    die("Veritabanı hatası: " . $e->getMessage());
}
?>
