<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$error = '';
$success = '';

// Settings tablosunu kontrol et ve yoksa oluştur
try {
    $db->query("SELECT 1 FROM settings LIMIT 1");
} catch (PDOException $e) {
    // Tablo yoksa oluştur
    $db->exec("
        CREATE TABLE IF NOT EXISTS settings (
            name VARCHAR(50) PRIMARY KEY,
            value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");

    // Varsayılan ayarları ekle
    $default_settings = [
        ['site_name', 'Apartman Yönetim Sistemi', 'Site başlığı'],
        ['site_description', 'Apartman ve site yönetimi için ka<PERSON> çözüm', 'Site açıklaması'],
        ['contact_email', '<EMAIL>', 'İletişim e-posta adresi'],
        ['contact_phone', '+90 ************', 'İletişim telefon numarası'],
        ['address', 'Örnek Mahallesi, Örnek Sokak No:1, İstanbul', 'Adres bilgisi'],
        ['maintenance_mode', '0', 'Bakım modu (1: aktif, 0: pasif)'],
        ['currency', 'TL', 'Para birimi'],
        ['date_format', 'd.m.Y', 'Tarih formatı'],
        ['time_format', 'H:i', 'Saat formatı'],
        ['items_per_page', '20', 'Sayfa başına öğe sayısı']
    ];

    $stmt = $db->prepare("INSERT INTO settings (name, value, description) VALUES (?, ?, ?)");
    foreach ($default_settings as $setting) {
        $stmt->execute($setting);
    }

    $success = 'Ayarlar tablosu oluşturuldu ve varsayılan değerler eklendi.';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $site_name = trim($_POST['site_name'] ?? '');
    $site_description = trim($_POST['site_description'] ?? '');
    $contact_email = trim($_POST['contact_email'] ?? '');
    $contact_phone = trim($_POST['contact_phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;

    if (!$site_name) {
        $error = 'Site adı zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE settings SET value=? WHERE name=?');
        $stmt->execute([$site_name, 'site_name']);
        $stmt->execute([$site_description, 'site_description']);
        $stmt->execute([$contact_email, 'contact_email']);
        $stmt->execute([$contact_phone, 'contact_phone']);
        $stmt->execute([$address, 'address']);
        $stmt->execute([$maintenance_mode, 'maintenance_mode']);

        $success = 'Ayarlar başarıyla güncellendi!';
    }
}

$settings = $db->query('SELECT name, value FROM settings')->fetchAll(PDO::FETCH_KEY_PAIR);
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Site Ayarları</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:800px;">
    <h2 class="mb-4">Site Ayarları</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Site Adı</label>
            <input type="text" name="site_name" class="form-control" value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Site Açıklaması</label>
            <textarea name="site_description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">İletişim E-posta</label>
            <input type="email" name="contact_email" class="form-control" value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">İletişim Telefon</label>
            <input type="text" name="contact_phone" class="form-control" value="<?php echo htmlspecialchars($settings['contact_phone'] ?? ''); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Adres</label>
            <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($settings['address'] ?? ''); ?></textarea>
        </div>
        <div class="mb-3">
            <div class="form-check">
                <input type="checkbox" name="maintenance_mode" class="form-check-input" id="maintenance_mode" <?php if(($settings['maintenance_mode'] ?? 0) == 1) echo 'checked'; ?>>
                <label class="form-check-label" for="maintenance_mode">Bakım Modu</label>
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Kaydet</button>
    </form>
</div>
</body>
</html>