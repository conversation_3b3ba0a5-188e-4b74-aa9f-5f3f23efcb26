<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();

$date1 = $_GET['date1'] ?? '';
$date2 = $_GET['date2'] ?? '';

$where_income = $where_expense = '';
$params_income = $params_expense = [];
if ($date1) {
    $where_income .= " AND payment_date >= ?";
    $where_expense .= " AND date >= ?";
    $params_income[] = $date1;
    $params_expense[] = $date1;
}
if ($date2) {
    $where_income .= " AND payment_date <= ?";
    $where_expense .= " AND date <= ?";
    $params_income[] = $date2;
    $params_expense[] = $date2;
}

// Aylık gelirler (aidat ve kira)
$incomes = $db->prepare("SELECT TO_CHAR(payment_date, 'YYYY-MM') as ay, SUM(amount) as toplam FROM payments WHERE 1=1 $where_income GROUP BY ay ORDER BY ay DESC");
$incomes->execute($params_income);
$incomes = $incomes->fetchAll(PDO::FETCH_KEY_PAIR);

// Aylık giderler
$expenses = $db->prepare("SELECT TO_CHAR(date, 'YYYY-MM') as ay, SUM(amount) as toplam FROM expenses WHERE 1=1 $where_expense GROUP BY ay ORDER BY ay DESC");
$expenses->execute($params_expense);
$expenses = $expenses->fetchAll(PDO::FETCH_KEY_PAIR);

// Tüm aylar birleştiriliyor
$months = array_unique(array_merge(array_keys($incomes), array_keys($expenses)));
sort($months);

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Gelir-Gider Tablosu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Gelir-Gider Tablosu</h2>
    <form class="row g-3 mb-4" method="get">
        <div class="col-md-3">
            <label class="form-label">Başlangıç Tarihi</label>
            <input type="date" name="date1" class="form-control" value="<?php echo htmlspecialchars($date1); ?>">
        </div>
        <div class="col-md-3">
            <label class="form-label">Bitiş Tarihi</label>
            <input type="date" name="date2" class="form-control" value="<?php echo htmlspecialchars($date2); ?>">
        </div>
        <div class="col-md-2 align-self-end">
            <button type="submit" class="btn btn-primary w-100">Filtrele</button>
        </div>
    </form>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
            <tr>
                <th>Ay</th>
                <th>Toplam Gelir</th>
                <th>Toplam Gider</th>
                <th>Bakiye</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($months as $m): ?>
            <tr>
                <td><?php echo htmlspecialchars($m); ?></td>
                <td><?php echo number_format($incomes[$m] ?? 0,2,',','.'); ?> ₺</td>
                <td><?php echo number_format($expenses[$m] ?? 0,2,',','.'); ?> ₺</td>
                <td><?php echo number_format(($incomes[$m] ?? 0) - ($expenses[$m] ?? 0),2,',','.'); ?> ₺</td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>
</body>
</html> 