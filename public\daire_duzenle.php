<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$blocks = $db->query('SELECT * FROM blocks ORDER BY name ASC')->fetchAll();

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: daireler.php');
    exit;
}
$id = (int)$_GET['id'];
$daire = $db->prepare('SELECT * FROM apartments WHERE id = ?');
$daire->execute([$id]);
$daire = $daire->fetch();
if (!$daire) {
    header('Location: daireler.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $block_id = $_POST['block_id'] ?? '';
    $number = trim($_POST['number'] ?? '');
    $floor = trim($_POST['floor'] ?? '');
    $owner_name = trim($_POST['owner_name'] ?? '');
    $owner_phone = trim($_POST['owner_phone'] ?? '');
    $owner_email = trim($_POST['owner_email'] ?? '');
    $tenant_name = trim($_POST['tenant_name'] ?? '');
    $tenant_phone = trim($_POST['tenant_phone'] ?? '');
    $tenant_email = trim($_POST['tenant_email'] ?? '');
    if (!$block_id || !$number || !$floor) {
        $error = 'Blok, daire no ve kat zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE apartments SET block_id=?, number=?, floor=?, owner_name=?, owner_phone=?, owner_email=?, tenant_name=?, tenant_phone=?, tenant_email=? WHERE id=?');
        $stmt->execute([$block_id, $number, $floor, $owner_name, $owner_phone, $owner_email, $tenant_name, $tenant_phone, $tenant_email, $id]);
        header('Location: daireler.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Daire Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:600px;">
    <h2 class="mb-4">Daire Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Blok</label>
            <select name="block_id" class="form-select" required>
                <option value="">Seçiniz</option>
                <?php foreach ($blocks as $block): ?>
                    <option value="<?php echo $block['id']; ?>" <?php if($daire['block_id']==$block['id']) echo 'selected'; ?>><?php echo htmlspecialchars($block['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Daire No</label>
            <input type="text" name="number" class="form-control" value="<?php echo htmlspecialchars($daire['number']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Kat</label>
            <input type="number" name="floor" class="form-control" value="<?php echo htmlspecialchars($daire['floor']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Sahip Adı</label>
            <input type="text" name="owner_name" class="form-control" value="<?php echo htmlspecialchars($daire['owner_name']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Sahip Telefon</label>
            <input type="text" name="owner_phone" class="form-control" value="<?php echo htmlspecialchars($daire['owner_phone']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Sahip E-Posta</label>
            <input type="email" name="owner_email" class="form-control" value="<?php echo htmlspecialchars($daire['owner_email']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Kiracı Adı</label>
            <input type="text" name="tenant_name" class="form-control" value="<?php echo htmlspecialchars($daire['tenant_name']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Kiracı Telefon</label>
            <input type="text" name="tenant_phone" class="form-control" value="<?php echo htmlspecialchars($daire['tenant_phone']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Kiracı E-Posta</label>
            <input type="email" name="tenant_email" class="form-control" value="<?php echo htmlspecialchars($daire['tenant_email']); ?>">
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="daireler.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 