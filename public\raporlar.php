<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();

// Özet veriler
$total_users = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
$total_apartments = $db->query("SELECT COUNT(*) FROM apartments")->fetchColumn();
$total_dues = $db->query("SELECT COALESCE(SUM(amount),0) FROM dues")->fetchColumn();
$total_debts = $db->query("SELECT COALESCE(SUM(amount),0) FROM dues WHERE status='unpaid' OR status='overdue'")->fetchColumn();
$total_expenses = $db->query("SELECT COALESCE(SUM(amount),0) FROM expenses")->fetchColumn();

// Aylık aidat tahsilatı (son 6 ay)
$dues_months = $db->query("
    SELECT TO_CHAR(due_date, 'YYYY-MM') as month, SUM(amount) as total
    FROM dues
    GROUP BY month
    ORDER BY month DESC
    LIMIT 6
")->fetchAll(PDO::FETCH_ASSOC);
$dues_months = array_reverse($dues_months);

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Raporlar & İstatistikler</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Raporlar & İstatistikler</h2>
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card text-bg-primary mb-3">
                <div class="card-body">
                    <h5 class="card-title">Kullanıcı</h5>
                    <p class="card-text fs-4"><?php echo $total_users; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-bg-success mb-3">
                <div class="card-body">
                    <h5 class="card-title">Daire</h5>
                    <p class="card-text fs-4"><?php echo $total_apartments; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-bg-info mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Aidat</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_dues,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-bg-danger mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Borç</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_debts,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-bg-warning mb-3">
                <div class="card-body">
                    <h5 class="card-title">Toplam Gider</h5>
                    <p class="card-text fs-4"><?php echo number_format($total_expenses,2,',','.'); ?> ₺</p>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-header">Son 6 Ay Aidat Tahsilatı</div>
        <div class="card-body">
            <canvas id="duesChart" height="80"></canvas>
        </div>
    </div>
</div>
<script>
const ctx = document.getElementById('duesChart').getContext('2d');
const duesChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode(array_column($dues_months, 'month')); ?>,
        datasets: [{
            label: 'Aidat Tahsilatı (₺)',
            data: <?php echo json_encode(array_map(fn($row) => (float)$row['total'], $dues_months)); ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.7)'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: { display: false },
            title: { display: false }
        },
        scales: {
            y: { beginAtZero: true }
        }
    }
});
</script>
</body>
</html>