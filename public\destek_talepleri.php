<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
$db = getDBConnection();
$supports = $db->query('SELECT s.id, s.title, s.created_at, u.full_name FROM support_requests s JOIN users u ON s.user_id = u.id ORDER BY s.created_at DESC')->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Destek <PERSON>i</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <h2 class="mb-4">Destek <PERSON></h2>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
            <tr>
                <th>#</th>
                <th>Kullanıcı</th>
                <th>Başlık</th>
                <th>Tarih</th>
                <th>Detay</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($supports as $s): ?>
            <tr>
                <td><?php echo $s['id']; ?></td>
                <td><?php echo htmlspecialchars($s['full_name']); ?></td>
                <td><?php echo htmlspecialchars($s['title']); ?></td>
                <td><?php echo date('d.m.Y H:i', strtotime($s['created_at'])); ?></td>
                <td><a href="destek_talep_detay.php?id=<?php echo $s['id']; ?>" class="btn btn-sm btn-info">Detay</a></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>
</body>
</html> 