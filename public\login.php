<?php
require_once '../config/functions.php';

// Kullanıcı değiştirme modu aktif değilse ve zaten giriş yapılmışsa ana sayfaya yönlendir
if (isLoggedIn() && !isset($_SESSION['switching_user'])) {
    redirect('index.php');
}

// Kullanıcı değiştirme modu aktifse, başlık ve mesaj ayarla
$switching_user = isset($_SESSION['switching_user']);
$title = $switching_user ? 'Kullanıcı Değiştir' : 'Giriş Yap';
$message = $switching_user ? 'Lütfen geçiş yapmak istediğiniz kullanıcı bilgilerini girin' : '';

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    if ($username && $password) {
        $db = getDBConnection();
        $stmt = $db->prepare('SELECT * FROM users WHERE username = ?');
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];

            // Kullanıcı değiştirme modunu temizle
            if (isset($_SESSION['switching_user'])) {
                unset($_SESSION['switching_user']);
            }

            redirect('index.php');
        } else {
            $error = 'Kullanıcı adı veya şifre hatalı!';
        }
    } else {
        $error = 'Lütfen tüm alanları doldurun!';
    }
}
?><!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title><?php echo $title; ?> - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5" style="max-width:400px;">
        <h2 class="mb-4"><?php echo $title; ?></h2>
        <?php if ($message): ?>
            <div class="alert alert-info"><?php echo $message; ?></div>
        <?php endif; ?>
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        <form method="post">
            <div class="mb-3">
                <label for="username" class="form-label">Kullanıcı Adı</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Şifre</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary"><?php echo $switching_user ? 'Kullanıcı Değiştir' : 'Giriş Yap'; ?></button>
                <?php if ($switching_user): ?>
                <a href="index.php" class="btn btn-secondary">İptal</a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</body>
</html>