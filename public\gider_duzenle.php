<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: giderler.php');
    exit;
}
$id = (int)$_GET['id'];
$gider = $db->prepare('SELECT * FROM expenses WHERE id = ?');
$gider->execute([$id]);
$gider = $gider->fetch();
if (!$gider) {
    header('Location: giderler.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $amount = trim($_POST['amount'] ?? '');
    $date = trim($_POST['date'] ?? '');
    $description = trim($_POST['description'] ?? '');
    if (!$title || !$amount || !$date) {
        $error = 'Başlık, tutar ve tarih zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE expenses SET title=?, amount=?, date=?, description=? WHERE id=?');
        $stmt->execute([$title, $amount, $date, $description, $id]);
        header('Location: giderler.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Gider Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Gider Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Başlık</label>
            <input type="text" name="title" class="form-control" value="<?php echo htmlspecialchars($gider['title']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Tutar (TL)</label>
            <input type="number" step="0.01" name="amount" class="form-control" value="<?php echo htmlspecialchars($gider['amount']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Tarih</label>
            <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($gider['date']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Açıklama</label>
            <textarea name="description" class="form-control" rows="2"><?php echo htmlspecialchars($gider['description']); ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="giderler.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 