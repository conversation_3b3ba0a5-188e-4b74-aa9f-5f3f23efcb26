<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin'])) {
    header('Location: index.php');
    exit;
}

// Varsayılan veritabanı bağlantı bilgileri
$default_db_config = [
    'host' => 'localhost',
    'port' => '5434',
    'dbname' => 'AYSDATA',
    'username' => 'postgres',
    'password' => '5213'
];

// Oturum veya çerezden kayıtlı bağlantı bilgilerini al
$db_config = isset($_SESSION['db_config']) ? $_SESSION['db_config'] : $default_db_config;

// Log dosyası
$log_file = '../sql/sql_log.txt';
if (!file_exists($log_file)) {
    file_put_contents($log_file, "SQL Manager Log File\n" . str_repeat('-', 50) . "\n");
}

// Mesajlar
$success_message = '';
$error_message = '';
$log_content = '';

// Bağlantı testi
if (isset($_POST['test_connection'])) {
    $db_config = [
        'host' => $_POST['db_host'],
        'port' => $_POST['db_port'],
        'dbname' => $_POST['db_name'],
        'username' => $_POST['db_user'],
        'password' => $_POST['db_pass']
    ];

    try {
        $dsn = "pgsql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $success_message = "Veritabanı bağlantısı başarılı!";
        $_SESSION['db_config'] = $db_config;

        // Log kaydı
        $log_entry = date('Y-m-d H:i:s') . " - Bağlantı testi başarılı: {$db_config['username']}@{$db_config['host']}:{$db_config['port']}/{$db_config['dbname']}\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND);
    } catch (PDOException $e) {
        $error_message = "Veritabanı bağlantı hatası: " . $e->getMessage();

        // Log kaydı
        $log_entry = date('Y-m-d H:i:s') . " - Bağlantı hatası: {$e->getMessage()}\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND);
    }
}

// SQL dosyasını çalıştır
if (isset($_POST['run_sql']) && isset($_POST['sql_file'])) {
    $sql_file = '../sql/' . $_POST['sql_file'];

    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);

        try {
            $dsn = "pgsql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // SQL çalıştırmadan önce mevcut tabloları al
            $tables_before = [];
            $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $tables_before[] = $row['table_name'];
            }

            // SQL'i çalıştır
            $pdo->exec($sql_content);

            // SQL çalıştırdıktan sonra tabloları al
            $tables_after = [];
            $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $tables_after[] = $row['table_name'];
            }

            // Yeni oluşturulan tabloları bul
            $new_tables = array_diff($tables_after, $tables_before);

            $success_message = "SQL sorgusu başarıyla çalıştırıldı: " . basename($sql_file);

            // Yeni tablolar için yapı bilgilerini al
            $table_structures = [];
            foreach ($new_tables as $table) {
                $columns = [];
                $stmt = $pdo->query("SELECT column_name, data_type, character_maximum_length, column_default, is_nullable
                                    FROM information_schema.columns
                                    WHERE table_schema = 'public' AND table_name = '$table'
                                    ORDER BY ordinal_position");
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $columns[] = $row;
                }
                $table_structures[$table] = $columns;
            }

            // Log kaydı
            $log_entry = date('Y-m-d H:i:s') . " - SQL çalıştırıldı: " . basename($sql_file) . "\n";
            $log_entry .= "SQL İçeriği:\n" . $sql_content . "\n";

            if (!empty($new_tables)) {
                $log_entry .= "Oluşturulan Tablolar:\n";
                foreach ($new_tables as $table) {
                    $log_entry .= "- $table\n";
                    $log_entry .= "  Sütunlar:\n";
                    foreach ($table_structures[$table] as $column) {
                        $type = $column['data_type'];
                        if (!empty($column['character_maximum_length'])) {
                            $type .= "({$column['character_maximum_length']})";
                        }
                        $nullable = $column['is_nullable'] === 'YES' ? 'NULL' : 'NOT NULL';
                        $default = !empty($column['column_default']) ? "DEFAULT {$column['column_default']}" : '';
                        $log_entry .= "    {$column['column_name']} $type $nullable $default\n";
                    }
                }
            } else {
                $log_entry .= "Yeni tablo oluşturulmadı. Mevcut tablolar güncellendi veya veri işlemi yapıldı.\n";
            }

            $log_entry .= str_repeat('-', 50) . "\n";
            file_put_contents($log_file, $log_entry, FILE_APPEND);
        } catch (PDOException $e) {
            $error_message = "SQL çalıştırma hatası: " . $e->getMessage();

            // Log kaydı
            $log_entry = date('Y-m-d H:i:s') . " - SQL hatası: " . basename($sql_file) . "\n";
            $log_entry .= "SQL İçeriği:\n" . $sql_content . "\n";
            $log_entry .= "Hata: " . $e->getMessage() . "\n";
            $log_entry .= str_repeat('-', 50) . "\n";
            file_put_contents($log_file, $log_entry, FILE_APPEND);
        }
    } else {
        $error_message = "SQL dosyası bulunamadı: " . basename($sql_file);
    }
}

// SQL dosyasını sil
if (isset($_POST['delete_sql']) && isset($_POST['sql_file'])) {
    $sql_file = '../sql/' . $_POST['sql_file'];

    if (file_exists($sql_file)) {
        if (unlink($sql_file)) {
            $success_message = "SQL dosyası silindi: " . basename($sql_file);

            // Log kaydı
            $log_entry = date('Y-m-d H:i:s') . " - SQL dosyası silindi: " . basename($sql_file) . "\n";
            file_put_contents($log_file, $log_entry, FILE_APPEND);
        } else {
            $error_message = "SQL dosyası silinemedi: " . basename($sql_file);

            // Log kaydı
            $log_entry = date('Y-m-d H:i:s') . " - SQL dosyası silinemedi: " . basename($sql_file) . "\n";
            file_put_contents($log_file, $log_entry, FILE_APPEND);
        }
    } else {
        $error_message = "SQL dosyası bulunamadı: " . basename($sql_file);
    }
}

// Yeni SQL dosyası oluştur
if (isset($_POST['create_sql']) && !empty($_POST['sql_filename']) && !empty($_POST['sql_content'])) {
    $filename = $_POST['sql_filename'];
    // Dosya adının .sql ile bitmesini sağla
    if (!preg_match('/\.sql$/i', $filename)) {
        $filename .= '.sql';
    }

    $sql_file = '../sql/' . $filename;

    if (file_put_contents($sql_file, $_POST['sql_content'])) {
        $success_message = "SQL dosyası oluşturuldu: " . $filename;

        // Log kaydı
        $log_entry = date('Y-m-d H:i:s') . " - SQL dosyası oluşturuldu: " . $filename . "\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND);
    } else {
        $error_message = "SQL dosyası oluşturulamadı: " . $filename;

        // Log kaydı
        $log_entry = date('Y-m-d H:i:s') . " - SQL dosyası oluşturulamadı: " . $filename . "\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND);
    }
}

// Log temizleme
if (isset($_POST['clear_log'])) {
    if (file_exists($log_file)) {
        // Log dosyasını temizle ama başlığı koru
        $log_content = "SQL Manager Log File\n" . str_repeat('-', 50) . "\n";
        $log_content .= date('Y-m-d H:i:s') . " - Log temizlendi\n";
        file_put_contents($log_file, $log_content);
        $success_message = "Log kayıtları temizlendi.";
    }
}

// Log içeriğini oku
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
}

// SQL dosyasının içeriğini görüntüle
if (isset($_POST['view_sql']) && isset($_POST['sql_file'])) {
    $sql_file = '../sql/' . $_POST['sql_file'];

    if (file_exists($sql_file)) {
        $sql_content_view = file_get_contents($sql_file);
        $sql_file_name = basename($sql_file);
    } else {
        $error_message = "SQL dosyası bulunamadı: " . basename($sql_file);
    }
}

// SQL dosyalarını listele
$sql_files = [];
$sql_dir = '../sql/';
if (is_dir($sql_dir)) {
    $files = scandir($sql_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $sql_files[] = $file;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>SQL Yöneticisi - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sql-file-item {
            border-left: 4px solid #0d6efd;
            margin-bottom: 8px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .log-container {
            background-color: #212529;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: 600;
        }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">SQL Yöneticisi</h2>
        <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
    </div>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Sol Kolon - Veritabanı Ayarları -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <i class="bi bi-database"></i> Veritabanı Bağlantı Ayarları
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">Sunucu Adresi</label>
                            <input type="text" name="db_host" class="form-control" value="<?php echo htmlspecialchars($db_config['host']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Port</label>
                            <input type="text" name="db_port" class="form-control" value="<?php echo htmlspecialchars($db_config['port']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Veritabanı Adı</label>
                            <input type="text" name="db_name" class="form-control" value="<?php echo htmlspecialchars($db_config['dbname']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Kullanıcı Adı</label>
                            <input type="text" name="db_user" class="form-control" value="<?php echo htmlspecialchars($db_config['username']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Şifre</label>
                            <input type="password" name="db_pass" class="form-control" value="<?php echo htmlspecialchars($db_config['password']); ?>" required>
                        </div>
                        <button type="submit" name="test_connection" class="btn btn-primary w-100">
                            <i class="bi bi-check-circle"></i> Bağlantıyı Test Et
                        </button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-success text-white">
                    <i class="bi bi-file-earmark-plus"></i> Yeni SQL Dosyası Oluştur
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">Dosya Adı</label>
                            <input type="text" name="sql_filename" class="form-control" placeholder="ornek.sql" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">SQL İçeriği</label>
                            <textarea name="sql_content" class="form-control" rows="6" placeholder="-- SQL sorgularınızı buraya yazın" required></textarea>
                        </div>
                        <button type="submit" name="create_sql" class="btn btn-success w-100">
                            <i class="bi bi-save"></i> Dosyayı Kaydet
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Orta Kolon - SQL Dosyaları -->
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <i class="bi bi-file-earmark-code"></i> SQL Dosyaları
                </div>
                <div class="card-body">
                    <?php if (empty($sql_files)): ?>
                        <div class="alert alert-warning">Henüz SQL dosyası bulunmuyor.</div>
                    <?php else: ?>
                        <?php foreach ($sql_files as $file): ?>
                            <div class="sql-file-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-code text-primary"></i>
                                    <span class="ms-2"><?php echo htmlspecialchars($file); ?></span>
                                </div>
                                <div>
                                    <form method="post" class="d-inline">
                                        <input type="hidden" name="sql_file" value="<?php echo htmlspecialchars($file); ?>">
                                        <button type="submit" name="view_sql" class="btn btn-sm btn-info" title="Görüntüle">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </form>
                                    <form method="post" class="d-inline">
                                        <input type="hidden" name="sql_file" value="<?php echo htmlspecialchars($file); ?>">
                                        <button type="submit" name="run_sql" class="btn btn-sm btn-success" title="Çalıştır">
                                            <i class="bi bi-play-fill"></i>
                                        </button>
                                    </form>
                                    <form method="post" class="d-inline" onsubmit="return confirm('Bu SQL dosyasını silmek istediğinize emin misiniz?');">
                                        <input type="hidden" name="sql_file" value="<?php echo htmlspecialchars($file); ?>">
                                        <button type="submit" name="delete_sql" class="btn btn-sm btn-danger" title="Sil">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sağ Kolon - Log -->
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-journal-text"></i> SQL İşlem Logları
                    </div>
                    <form method="post" onsubmit="return confirm('Log kayıtlarını temizlemek istediğinize emin misiniz?');">
                        <button type="submit" name="clear_log" class="btn btn-sm btn-outline-light">
                            <i class="bi bi-trash"></i> Logu Temizle
                        </button>
                    </form>
                </div>
                <div class="card-body">
                    <div class="log-container">
                        <?php echo htmlspecialchars($log_content); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- SQL İçerik Görüntüleme Modal -->
<?php if (isset($sql_content_view)): ?>
<div class="modal fade" id="sqlViewModal" tabindex="-1" aria-labelledby="sqlViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sqlViewModalLabel"><?php echo htmlspecialchars($sql_file_name); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"><?php echo htmlspecialchars($sql_content_view); ?></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <form method="post" class="d-inline">
                    <input type="hidden" name="sql_file" value="<?php echo htmlspecialchars($sql_file_name); ?>">
                    <button type="submit" name="run_sql" class="btn btn-success">
                        <i class="bi bi-play-fill"></i> Çalıştır
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var sqlViewModal = new bootstrap.Modal(document.getElementById('sqlViewModal'));
        sqlViewModal.show();
    });
</script>
<?php endif; ?>

</body>
</html>
