<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$daireler = $db->query('SELECT a.*, b.name as block_name FROM apartments a LEFT JOIN blocks b ON a.block_id = b.id ORDER BY a.id DESC')->fetchAll();
$bloklar = $db->query('SELECT * FROM blocks ORDER BY name')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM apartments WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: daireler.php');
    exit;
}

// Blok ekleme işlemi
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_block'])) {
    $name = trim($_POST['block_name'] ?? '');
    $description = trim($_POST['description'] ?? '');

    if (!$name) {
        $error = 'Blok adı zorunludur!';
    } else {
        // Aynı isimde blok var mı kontrol et
        $check = $db->prepare('SELECT COUNT(*) FROM blocks WHERE name = ?');
        $check->execute([$name]);
        if ($check->fetchColumn() > 0) {
            $error = 'Bu isimde bir blok zaten mevcut!';
        } else {
            $stmt = $db->prepare('INSERT INTO blocks (name, description, created_at) VALUES (?, ?, NOW())');
            $stmt->execute([$name, $description]);
            $success = 'Blok başarıyla eklendi!';

            // Blok listesini güncelle
            $bloklar = $db->query('SELECT * FROM blocks ORDER BY name')->fetchAll();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Daireler - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Daireler</h2>
        <div>
            <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#viewBlocksModal">
                <i class="bi bi-buildings"></i> Mevcut Bloklar
            </button>
            <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addBlockModal">
                <i class="bi bi-building"></i> Blok Ekle
            </button>
            <a href="daire_ekle.php" class="btn btn-success"><i class="bi bi-plus"></i> Yeni Daire</a>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
        <tr>
            <th>#</th>
            <th>Blok</th>
            <th>Daire No</th>
            <th>Kat</th>
            <th>Sahip</th>
            <th>Kiracı</th>
            <th>İşlemler</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($daireler as $daire): ?>
            <tr>
                <td><?php echo $daire['id']; ?></td>
                <td><?php echo htmlspecialchars($daire['block_name']); ?></td>
                <td><?php echo htmlspecialchars($daire['number']); ?></td>
                <td><?php echo htmlspecialchars($daire['floor']); ?></td>
                <td><?php echo htmlspecialchars($daire['owner_name']); ?></td>
                <td><?php echo htmlspecialchars($daire['tenant_name']); ?></td>
                <td>
                    <a href="daire_duzenle.php?id=<?php echo $daire['id']; ?>" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                    <a href="?sil=<?php echo $daire['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Daire silinsin mi?');"><i class="bi bi-trash"></i></a>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-secondary mt-3"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
</div>

<!-- Blok Ekleme Modal -->
<div class="modal fade" id="addBlockModal" tabindex="-1" aria-labelledby="addBlockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addBlockModalLabel">Yeni Blok Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post">
                    <div class="mb-3">
                        <label for="block_name" class="form-label">Blok Adı <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="block_name" name="block_name" required>
                        <div class="form-text">Örnek: A Blok, B Blok, vb.</div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">İptal</button>
                        <button type="submit" name="add_block" class="btn btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Mevcut Bloklar Modal -->
<div class="modal fade" id="viewBlocksModal" tabindex="-1" aria-labelledby="viewBlocksModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewBlocksModalLabel">Mevcut Bloklar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Blok Adı</th>
                            <th>Açıklama</th>
                            <th>Oluşturulma Tarihi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($bloklar as $blok): ?>
                            <tr>
                                <td><?php echo $blok['id']; ?></td>
                                <td><?php echo htmlspecialchars($blok['name']); ?></td>
                                <td><?php echo htmlspecialchars($blok['description']); ?></td>
                                <td><?php echo date('d.m.Y H:i', strtotime($blok['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <?php if (empty($bloklar)): ?>
                            <tr>
                                <td colspan="4" class="text-center">Henüz blok bulunmuyor.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBlockModal">
                    <i class="bi bi-plus"></i> Yeni Blok Ekle
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Başarı mesajı varsa, sayfayı yeniledikten sonra 3 saniye içinde gizle
    document.addEventListener('DOMContentLoaded', function() {
        const successAlert = document.querySelector('.alert-success');
        if (successAlert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(successAlert);
                bsAlert.close();
            }, 3000);
        }
    });
</script>
</body>
</html>