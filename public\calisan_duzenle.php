<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();

// ID kontrolü
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: calisanlar.php');
    exit;
}

$id = (int)$_GET['id'];
$calisan = $db->prepare('SELECT * FROM employees WHERE id = ?');
$calisan->execute([$id]);
$calisan = $calisan->fetch();

if (!$calisan) {
    header('Location: calisanlar.php');
    exit;
}

// Pozisyon listesi
$positions = [
    'Kapıcı',
    'Güvenlik',
    'Temizlik Görevlisi',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>k<PERSON> Personel',
    'Yönetici',
    'Diğer'
];

// Çalışan güncelleme
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $start_date = trim($_POST['start_date'] ?? '');
    $salary = trim($_POST['salary'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    if (!$name || !$position || !$phone) {
        $error = 'Ad, pozisyon ve telefon zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE employees SET name = ?, position = ?, phone = ?, email = ?, start_date = ?, salary = ?, notes = ? WHERE id = ?');
        $stmt->execute([$name, $position, $phone, $email, $start_date, $salary, $notes, $id]);
        $success = 'Çalışan bilgileri başarıyla güncellendi!';
        
        // Güncel bilgileri al
        $calisan = $db->prepare('SELECT * FROM employees WHERE id = ?');
        $calisan->execute([$id]);
        $calisan = $calisan->fetch();
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Çalışan Düzenle - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Çalışan Düzenle</h2>
        <a href="calisanlar.php" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Geri Dön
        </a>
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-body">
            <form method="post">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Ad Soyad <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($calisan['name']); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Pozisyon <span class="text-danger">*</span></label>
                        <select name="position" class="form-select" required>
                            <option value="">Seçiniz</option>
                            <?php foreach ($positions as $position): ?>
                                <option value="<?php echo htmlspecialchars($position); ?>" <?php if ($calisan['position'] === $position) echo 'selected'; ?>>
                                    <?php echo htmlspecialchars($position); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Telefon <span class="text-danger">*</span></label>
                        <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($calisan['phone']); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">E-posta</label>
                        <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($calisan['email']); ?>">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">İşe Başlama Tarihi</label>
                        <input type="date" name="start_date" class="form-control" value="<?php echo $calisan['start_date']; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Maaş (₺)</label>
                        <input type="number" name="salary" class="form-control" step="0.01" value="<?php echo $calisan['salary']; ?>">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Notlar</label>
                    <textarea name="notes" class="form-control" rows="3"><?php echo htmlspecialchars($calisan['notes']); ?></textarea>
                </div>
                <div class="text-end">
                    <a href="calisanlar.php" class="btn btn-secondary">İptal</a>
                    <button type="submit" class="btn btn-primary">Güncelle</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
