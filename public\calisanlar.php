<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();

// Çalışanları getir
$calisanlar = $db->query('SELECT * FROM employees ORDER BY name')->fetchAll();

// Çalışan silme
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    $stmt = $db->prepare('DELETE FROM employees WHERE id = ?');
    $stmt->execute([$id]);
    header('Location: calisanlar.php');
    exit;
}

// Çalışan ekleme
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_employee'])) {
    $name = trim($_POST['name'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $start_date = trim($_POST['start_date'] ?? '');
    $salary = trim($_POST['salary'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    if (!$name || !$position || !$phone) {
        $error = 'Ad, pozisyon ve telefon zorunludur!';
    } else {
        $stmt = $db->prepare('INSERT INTO employees (name, position, phone, email, start_date, salary, notes, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())');
        $stmt->execute([$name, $position, $phone, $email, $start_date, $salary, $notes]);
        $success = 'Çalışan başarıyla eklendi!';
        // Sayfayı yenile
        header('Location: calisanlar.php');
        exit;
    }
}

// Pozisyon listesi
$positions = [
    'Kapıcı',
    'Güvenlik',
    'Temizlik Görevlisi',
    'Bahçıvan',
    'Teknik Personel',
    'Yönetici',
    'Diğer'
];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Çalışanlar - Apartman Yönetim Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .employee-card {
            transition: transform 0.2s;
            height: 100%;
        }
        .employee-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .position-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Çalışanlar</h2>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
            <i class="bi bi-plus-lg"></i> Yeni Çalışan Ekle
        </button>
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <!-- Pozisyon Filtreleme -->
    <div class="mb-4">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary active filter-btn" data-filter="all">Tümü</button>
            <?php foreach ($positions as $position): ?>
                <button type="button" class="btn btn-outline-primary filter-btn" data-filter="<?php echo htmlspecialchars($position); ?>">
                    <?php echo htmlspecialchars($position); ?>
                </button>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- Çalışan Listesi -->
    <div class="row g-4" id="employees-container">
        <?php if (empty($calisanlar)): ?>
            <div class="col-12">
                <div class="alert alert-info">Henüz kayıtlı çalışan bulunmuyor.</div>
            </div>
        <?php else: ?>
            <?php foreach ($calisanlar as $calisan): ?>
                <div class="col-md-6 col-lg-4 employee-item" data-position="<?php echo htmlspecialchars($calisan['position']); ?>">
                    <div class="card employee-card">
                        <div class="card-body">
                            <span class="badge bg-primary position-badge"><?php echo htmlspecialchars($calisan['position']); ?></span>
                            <h5 class="card-title"><?php echo htmlspecialchars($calisan['name']); ?></h5>
                            <p class="card-text mb-1">
                                <i class="bi bi-telephone"></i> <?php echo htmlspecialchars($calisan['phone']); ?>
                            </p>
                            <?php if ($calisan['email']): ?>
                                <p class="card-text mb-1">
                                    <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($calisan['email']); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($calisan['start_date']): ?>
                                <p class="card-text mb-1">
                                    <i class="bi bi-calendar-check"></i> Başlangıç: <?php echo date('d.m.Y', strtotime($calisan['start_date'])); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($calisan['salary']): ?>
                                <p class="card-text mb-1">
                                    <i class="bi bi-cash"></i> Maaş: <?php echo number_format($calisan['salary'], 2, ',', '.'); ?> ₺
                                </p>
                            <?php endif; ?>
                            <?php if ($calisan['notes']): ?>
                                <p class="card-text mt-2">
                                    <small><?php echo nl2br(htmlspecialchars($calisan['notes'])); ?></small>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent d-flex justify-content-between">
                            <small class="text-muted">Eklenme: <?php echo date('d.m.Y', strtotime($calisan['created_at'])); ?></small>
                            <div>
                                <a href="calisan_duzenle.php?id=<?php echo $calisan['id']; ?>" class="btn btn-sm btn-warning">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="?sil=<?php echo $calisan['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bu çalışanı silmek istediğinize emin misiniz?');">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <a href="index.php" class="btn btn-secondary mt-4">
        <i class="bi bi-arrow-left"></i> Ana Sayfa
    </a>
</div>

<!-- Yeni Çalışan Ekleme Modal -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Yeni Çalışan Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Ad Soyad <span class="text-danger">*</span></label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Pozisyon <span class="text-danger">*</span></label>
                            <select name="position" class="form-select" required>
                                <option value="">Seçiniz</option>
                                <?php foreach ($positions as $position): ?>
                                    <option value="<?php echo htmlspecialchars($position); ?>"><?php echo htmlspecialchars($position); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Telefon <span class="text-danger">*</span></label>
                            <input type="text" name="phone" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">E-posta</label>
                            <input type="email" name="email" class="form-control">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">İşe Başlama Tarihi</label>
                            <input type="date" name="start_date" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Maaş (₺)</label>
                            <input type="number" name="salary" class="form-control" step="0.01">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notlar</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                        <button type="submit" name="add_employee" class="btn btn-success">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Pozisyon filtreleme
        const filterButtons = document.querySelectorAll('.filter-btn');
        const employeeItems = document.querySelectorAll('.employee-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Aktif butonu güncelle
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                
                employeeItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-position') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
</body>
</html>
