<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: destek_talepleri.php');
    exit;
}
$id = (int)$_GET['id'];
$db = getDBConnection();
$stmt = $db->prepare('SELECT s.*, u.full_name, u.email FROM support_requests s JOIN users u ON s.user_id = u.id WHERE s.id = ?');
$stmt->execute([$id]);
$s = $stmt->fetch();
if (!$s) {
    header('Location: destek_talepleri.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Destek Talebi Detay</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:700px;">
    <h2 class="mb-4">Destek Talebi Detay</h2>
    <div class="card mb-3">
        <div class="card-header">Kullanıcı Bilgileri</div>
        <div class="card-body">
            <b>Ad Soyad:</b> <?php echo htmlspecialchars($s['full_name']); ?><br>
            <b>E-posta:</b> <?php echo htmlspecialchars($s['email']); ?>
        </div>
    </div>
    <div class="card mb-3">
        <div class="card-header">Talep Bilgileri</div>
        <div class="card-body">
            <b>Başlık:</b> <?php echo htmlspecialchars($s['title']); ?><br>
            <b>Tarih:</b> <?php echo date('d.m.Y H:i', strtotime($s['created_at'])); ?><br>
            <b>Açıklama:</b><br>
            <div class="border rounded p-2 bg-light mt-2"><?php echo nl2br(htmlspecialchars($s['message'])); ?></div>
        </div>
    </div>
    <a href="destek_talepleri.php" class="btn btn-secondary">Geri Dön</a>
</div>
</body>
</html> 