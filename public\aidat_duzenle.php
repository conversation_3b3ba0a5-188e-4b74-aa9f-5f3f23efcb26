<?php
require_once '../config/functions.php';
requireLogin();
$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'manager'])) {
    header('Location: index.php');
    exit;
}

$db = getDBConnection();
$daireler = $db->query('SELECT a.id, a.number, b.name as block_name FROM apartments a LEFT JOIN blocks b ON a.block_id = b.id ORDER BY b.name, a.number')->fetchAll();

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: aidatlar.php');
    exit;
}
$id = (int)$_GET['id'];
$aidat = $db->prepare('SELECT * FROM dues WHERE id = ?');
$aidat->execute([$id]);
$aidat = $aidat->fetch();
if (!$aidat) {
    header('Location: aidatlar.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $apartment_id = $_POST['apartment_id'] ?? '';
    $amount = trim($_POST['amount'] ?? '');
    $due_date = trim($_POST['due_date'] ?? '');
    $status = $_POST['status'] ?? 'unpaid';
    $payment_date = trim($_POST['payment_date'] ?? '');
    if (!$apartment_id || !$amount || !$due_date) {
        $error = 'Daire, tutar ve son ödeme tarihi zorunludur!';
    } else {
        $stmt = $db->prepare('UPDATE dues SET apartment_id=?, amount=?, due_date=?, status=?, payment_date=? WHERE id=?');
        $stmt->execute([$apartment_id, $amount, $due_date, $status, $payment_date ?: null, $id]);
        header('Location: aidatlar.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Aidat Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Aidat Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Daire</label>
            <select name="apartment_id" class="form-select" required>
                <option value="">Seçiniz</option>
                <?php foreach ($daireler as $daire): ?>
                    <option value="<?php echo $daire['id']; ?>" <?php if($aidat['apartment_id']==$daire['id']) echo 'selected'; ?>><?php echo htmlspecialchars($daire['block_name'] . ' - ' . $daire['number']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Tutar (TL)</label>
            <input type="number" step="0.01" name="amount" class="form-control" value="<?php echo htmlspecialchars($aidat['amount']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Son Ödeme Tarihi</label>
            <input type="date" name="due_date" class="form-control" value="<?php echo htmlspecialchars($aidat['due_date']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Durum</label>
            <select name="status" class="form-select">
                <option value="unpaid" <?php if($aidat['status']==='unpaid') echo 'selected'; ?>>Ödenmedi</option>
                <option value="paid" <?php if($aidat['status']==='paid') echo 'selected'; ?>>Ödendi</option>
                <option value="overdue" <?php if($aidat['status']==='overdue') echo 'selected'; ?>>Gecikmiş</option>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Ödeme Tarihi (varsa)</label>
            <input type="date" name="payment_date" class="form-control" value="<?php echo htmlspecialchars($aidat['payment_date']); ?>">
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="aidatlar.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 