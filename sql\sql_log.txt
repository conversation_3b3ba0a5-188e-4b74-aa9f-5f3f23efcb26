SQL Manager Log File
--------------------------------------------------
2025-05-18 13:33:15 - Log temizlendi
2025-05-18 13:33:16 - S<PERSON> hatası: create_blocks_table.sql
SQL İçeriği:
-- Bloklar tablosu
CREATE TABLE IF NOT EXISTS blocks (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> bloklar
INSERT INTO blocks (name, description, created_at) VALUES
('A Blok', 'Ana giriş kapısına en yakın blok', NOW()),
('B Blok', 'Orta blok', NOW()),
('<PERSON> Blok', 'Otopark tarafındaki blok', NOW())
ON CONFLICT (name) DO NOTHING;

Hata: SQLSTATE[42P10]: Invalid column reference: 7 ERROR:  there is no unique or exclusion constraint matching the ON CONFLICT specification
--------------------------------------------------
2025-05-18 13:35:44 - SQL çalıştırıldı: create_blocks_table.sql
SQL İçeriği:
-- Bloklar tablosu
CREATE TABLE IF NOT EXISTS blocks (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- name sütunu için UNIQUE kısıtlaması ekle
ALTER TABLE blocks ADD CONSTRAINT blocks_name_unique UNIQUE (name);

-- Örnek bloklar
DO $$
BEGIN
    -- A Blok
    IF NOT EXISTS (SELECT 1 FROM blocks WHERE name = 'A Blok') THEN
        INSERT INTO blocks (name, description, created_at) VALUES
        ('A Blok', 'Ana giriş kapısına en yakın blok', NOW());
    END IF;

    -- B Blok
    IF NOT EXISTS (SELECT 1 FROM blocks WHERE name = 'B Blok') THEN
        INSERT INTO blocks (name, description, created_at) VALUES
        ('B Blok', 'Orta blok', NOW());
    END IF;

    -- C Blok
    IF NOT EXISTS (SELECT 1 FROM blocks WHERE name = 'C Blok') THEN
        INSERT INTO blocks (name, description, created_at) VALUES
        ('C Blok', 'Otopark tarafındaki blok', NOW());
    END IF;
END $$;

Yeni tablo oluşturulmadı. Mevcut tablolar güncellendi veya veri işlemi yapıldı.
--------------------------------------------------
