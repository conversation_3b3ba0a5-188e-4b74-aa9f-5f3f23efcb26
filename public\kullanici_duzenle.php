<?php
require_once '../config/functions.php';
requireLogin();
requireRole('admin');

$db = getDBConnection();

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: kullanicilar.php');
    exit;
}
$id = (int)$_GET['id'];
$user = $db->prepare('SELECT * FROM users WHERE id = ?');
$user->execute([$id]);
$user = $user->fetch();
if (!$user) {
    header('Location: kullanicilar.php');
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $role = $_POST['role'] ?? 'resident';
    $password = $_POST['password'] ?? '';

    if (!$full_name || !$username || !$email) {
        $error = 'Tüm alanları doldurun!';
    } else {
        // Kullanıcı adı veya e-posta başka bir kullanıcıya ait mi?
        $stmt = $db->prepare('SELECT COUNT(*) FROM users WHERE (username = ? OR email = ?) AND id != ?');
        $stmt->execute([$username, $email, $id]);
        if ($stmt->fetchColumn() > 0) {
            $error = 'Bu kullanıcı adı veya e-posta başka bir kullanıcıya ait!';
        } else {
            if ($password) {
                $stmt = $db->prepare('UPDATE users SET username=?, email=?, full_name=?, role=?, password=? WHERE id=?');
                $stmt->execute([
                    $username, $email, $full_name, $role, password_hash($password, PASSWORD_DEFAULT), $id
                ]);
            } else {
                $stmt = $db->prepare('UPDATE users SET username=?, email=?, full_name=?, role=? WHERE id=?');
                $stmt->execute([
                    $username, $email, $full_name, $role, $id
                ]);
            }
            header('Location: kullanicilar.php');
            exit;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Kullanıcı Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4" style="max-width:500px;">
    <h2 class="mb-4">Kullanıcı Düzenle</h2>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">İsim Soyisim</label>
            <input type="text" name="full_name" class="form-control" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Kullanıcı Adı</label>
            <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">E-Posta</label>
            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Şifre (değiştirmek için doldurun)</label>
            <input type="password" name="password" class="form-control">
        </div>
        <div class="mb-3">
            <label class="form-label">Rol</label>
            <select name="role" class="form-select">
                <option value="admin" <?php if($user['role']==='admin') echo 'selected'; ?>>Admin</option>
                <option value="manager" <?php if($user['role']==='manager') echo 'selected'; ?>>Yönetici</option>
                <option value="resident" <?php if($user['role']==='resident') echo 'selected'; ?>>Sakin</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">Güncelle</button>
        <a href="kullanicilar.php" class="btn btn-secondary">İptal</a>
    </form>
</div>
</body>
</html> 