-- AYSDATA Veritabanı Yedeği
-- Oluşturulma Tarihi: 2025-05-18 14:02:20

-- <PERSON><PERSON><PERSON>: complaints

CREATE TABLE IF NOT EXISTS complaints (
    id integer NOT NULL DEFAULT nextval('complaints_id_seq'::regclass),
    title character varying(100) NOT NULL,
    content text NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    user_id integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON>: notes

CREATE TABLE IF NOT EXISTS notes (
    id integer NOT NULL DEFAULT nextval('notes_id_seq'::regclass),
    user_id integer,
    title character varying(100) NOT NULL,
    content text NOT NULL,
    color character varying(20) DEFAULT '#ffffff'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> verileri: notes
INSERT INTO notes (id, user_id, title, content, color, created_at, updated_at) VALUES (1, 1, 'Yeni not-01', 'Bu ay toplantı 20.05.205 te bodrumdaki salonda saat:17:00 da yapılacak.1 gün öncesi duyuru yapmayı unutma.', '#f8d7da', '2025-05-18 13:09:07.367254', '2025-05-18 13:09:07.367254');

-- Tablo: blocks

CREATE TABLE IF NOT EXISTS blocks (
    id integer NOT NULL DEFAULT nextval('blocks_id_seq'::regclass),
    name character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo verileri: blocks
INSERT INTO blocks (id, name, description, created_at) VALUES (1, 'A Blok', 'Ana giriş kapısına en yakın blok', '2025-05-18 13:35:44.417318');
INSERT INTO blocks (id, name, description, created_at) VALUES (2, 'B Blok', 'Orta blok', '2025-05-18 13:35:44.417318');
INSERT INTO blocks (id, name, description, created_at) VALUES (3, 'C Blok', 'Otopark tarafındaki blok', '2025-05-18 13:35:44.417318');

-- Tablo: apartments

CREATE TABLE IF NOT EXISTS apartments (
    id integer NOT NULL DEFAULT nextval('apartments_id_seq'::regclass),
    block_id integer,
    number character varying(10) NOT NULL,
    floor integer NOT NULL,
    owner_name character varying(100),
    owner_phone character varying(20),
    owner_email character varying(100),
    tenant_name character varying(100),
    tenant_phone character varying(20),
    tenant_email character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: dues

CREATE TABLE IF NOT EXISTS dues (
    id integer NOT NULL DEFAULT nextval('dues_id_seq'::regclass),
    apartment_id integer,
    amount numeric NOT NULL,
    due_date date NOT NULL,
    status character varying(20) DEFAULT 'unpaid'::character varying,
    payment_date date,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: users

CREATE TABLE IF NOT EXISTS users (
    id integer NOT NULL DEFAULT nextval('users_id_seq'::regclass),
    username character varying(50) NOT NULL,
    password character varying(255) NOT NULL,
    email character varying(100) NOT NULL,
    full_name character varying(100) NOT NULL,
    role character varying(20) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo verileri: users
INSERT INTO users (id, username, password, email, full_name, role, created_at, updated_at) VALUES (1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Admin Kullanıcı', 'admin', '2025-05-14 21:02:22.659349', '2025-05-14 21:02:22.659349');
INSERT INTO users (id, username, password, email, full_name, role, created_at, updated_at) VALUES (3, 'yonetici', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Yönetici Kullanıcı', 'manager', '2025-05-14 21:06:40.072874', '2025-05-14 21:06:40.072874');
INSERT INTO users (id, username, password, email, full_name, role, created_at, updated_at) VALUES (4, 'sakin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Sakin Kullanıcı', 'resident', '2025-05-14 21:06:40.072874', '2025-05-14 21:06:40.072874');

-- Tablo: expenses

CREATE TABLE IF NOT EXISTS expenses (
    id integer NOT NULL DEFAULT nextval('expenses_id_seq'::regclass),
    category character varying(50) NOT NULL,
    description text NOT NULL,
    amount numeric NOT NULL,
    expense_date date NOT NULL,
    document_path character varying(255),
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: announcements

CREATE TABLE IF NOT EXISTS announcements (
    id integer NOT NULL DEFAULT nextval('announcements_id_seq'::regclass),
    title character varying(100) NOT NULL,
    content text NOT NULL,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: messages

CREATE TABLE IF NOT EXISTS messages (
    id integer NOT NULL DEFAULT nextval('messages_id_seq'::regclass),
    sender_id integer,
    receiver_id integer,
    subject character varying(100) NOT NULL,
    content text NOT NULL,
    status character varying(20) DEFAULT 'unread'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: cash_register

CREATE TABLE IF NOT EXISTS cash_register (
    id integer NOT NULL DEFAULT nextval('cash_register_id_seq'::regclass),
    transaction_type character varying(20) NOT NULL,
    amount numeric NOT NULL,
    description text NOT NULL,
    transaction_date date NOT NULL,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: payments

CREATE TABLE IF NOT EXISTS payments (
    id integer NOT NULL DEFAULT nextval('payments_id_seq'::regclass),
    apartment_id integer,
    amount numeric NOT NULL,
    payment_date date NOT NULL,
    payment_method character varying(20) NOT NULL,
    description text,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: services

CREATE TABLE IF NOT EXISTS services (
    id integer NOT NULL DEFAULT nextval('services_id_seq'::regclass),
    name character varying(100) NOT NULL,
    category character varying(50) NOT NULL,
    contact_person character varying(100),
    phone character varying(20) NOT NULL,
    email character varying(100),
    address text,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: settings

CREATE TABLE IF NOT EXISTS settings (
    name character varying(50) NOT NULL,
    value text,
    description text,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo verileri: settings
INSERT INTO settings (name, value, description, updated_at) VALUES ('site_name', 'Apartman Yönetim Sistemi', 'Site başlığı', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('site_description', 'Apartman ve site yönetimi için kapsamlı çözüm', 'Site açıklaması', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('contact_email', '<EMAIL>', 'İletişim e-posta adresi', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('contact_phone', '+90 ************', 'İletişim telefon numarası', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('address', 'Örnek Mahallesi, Örnek Sokak No:1, İstanbul', 'Adres bilgisi', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('maintenance_mode', 0, 'Bakım modu (1: aktif, 0: pasif)', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('currency', 'TL', 'Para birimi', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('date_format', 'd.m.Y', 'Tarih formatı', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('time_format', 'H:i', 'Saat formatı', '2025-05-18 13:13:08.764743');
INSERT INTO settings (name, value, description, updated_at) VALUES ('items_per_page', 20, 'Sayfa başına öğe sayısı', '2025-05-18 13:13:08.764743');

-- Tablo: maintenance_requests

CREATE TABLE IF NOT EXISTS maintenance_requests (
    id integer NOT NULL DEFAULT nextval('maintenance_requests_id_seq'::regclass),
    apartment_id integer,
    title character varying(100) NOT NULL,
    description text NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    scheduled_date date,
    completed_date date,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: support_requests

CREATE TABLE IF NOT EXISTS support_requests (
    id integer NOT NULL DEFAULT nextval('support_requests_id_seq'::regclass),
    title character varying(100) NOT NULL,
    content text NOT NULL,
    status character varying(20) DEFAULT 'open'::character varying,
    user_id integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Tablo: employees

CREATE TABLE IF NOT EXISTS employees (
    id integer NOT NULL DEFAULT nextval('employees_id_seq'::regclass),
    name character varying(100) NOT NULL,
    position character varying(50) NOT NULL,
    phone character varying(20) NOT NULL,
    email character varying(100),
    start_date date,
    salary numeric,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

