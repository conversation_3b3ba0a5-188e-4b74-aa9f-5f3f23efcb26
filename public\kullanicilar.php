<?php
require_once '../config/functions.php';
requireLogin();
requireRole('admin');

$db = getDBConnection();
$users = $db->query('SELECT * FROM users ORDER BY id DESC')->fetchAll();

// Silme işlemi
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = (int)$_GET['sil'];
    if ($id !== $_SESSION['user_id']) { // Kendi hesabını silemesin
        $stmt = $db->prepare('DELETE FROM users WHERE id = ?');
        $stmt->execute([$id]);
        header('Location: kullanicilar.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Kullanıcılar - Apartman <PERSON>ö<PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Kullanıcılar</h2>
        <a href="kullanici_ekle.php" class="btn btn-success"><i class="bi bi-plus"></i> Yeni Kullanıcı</a>
    </div>
    <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
        <tr>
            <th>#</th>
            <th>İsim</th>
            <th>Kullanıcı Adı</th>
            <th>E-Posta</th>
            <th>Rol</th>
            <th>İşlemler</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($users as $user): ?>
            <tr>
                <td><?php echo $user['id']; ?></td>
                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                <td><?php echo htmlspecialchars($user['username']); ?></td>
                <td><?php echo htmlspecialchars($user['email']); ?></td>
                <td>
                    <?php if ($user['role'] === 'admin'): ?>
                        <span class="badge bg-danger">Admin</span>
                    <?php elseif ($user['role'] === 'manager'): ?>
                        <span class="badge bg-primary">Yönetici</span>
                    <?php else: ?>
                        <span class="badge bg-secondary">Sakin</span>
                    <?php endif; ?>
                </td>
                <td>
                    <a href="kullanici_duzenle.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                    <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                        <a href="?sil=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Kullanıcı silinsin mi?');"><i class="bi bi-trash"></i></a>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-secondary mt-3"><i class="bi bi-arrow-left"></i> Ana Sayfa</a>
</div>
</body>
</html>